"""
ASCII Chart Generation Utilities for Autonomous System Manager
Provides terminal-based data visualization without external dependencies
"""

import math
from typing import List, Tuple, Optional, Dict, Any
from datetime import datetime, timedelta


class ASCIIChart:
    """Generate ASCII charts for terminal display"""
    
    def __init__(self, width: int = 60, height: int = 10):
        self.width = width
        self.height = height
        self.box_chars = {
            'horizontal': '─',
            'vertical': '│',
            'top_left': '┌',
            'top_right': '┐',
            'bottom_left': '└',
            'bottom_right': '┘',
            'cross': '┼',
            'top_tee': '┬',
            'bottom_tee': '┴',
            'left_tee': '├',
            'right_tee': '┤'
        }
        self.plot_chars = ['▁', '▂', '▃', '▄', '▅', '▆', '▇', '█']
    
    def line_chart(self, data: List[float], title: str = "", 
                   x_labels: Optional[List[str]] = None,
                   y_label: str = "", min_val: Optional[float] = None,
                   max_val: Optional[float] = None) -> str:
        """
        Generate ASCII line chart
        
        Args:
            data: List of numeric values
            title: Chart title
            x_labels: Labels for x-axis
            y_label: Label for y-axis
            min_val: Minimum y-axis value (auto if None)
            max_val: Maximum y-axis value (auto if None)
        """
        if not data:
            return "No data to display"
        
        # Determine value range
        data_min = min_val if min_val is not None else min(data)
        data_max = max_val if max_val is not None else max(data)
        
        if data_max == data_min:
            data_max = data_min + 1
        
        chart_lines = []
        
        # Add title
        if title:
            chart_lines.append(title)
            chart_lines.append("")
        
        # Create y-axis labels and chart area
        y_step = (data_max - data_min) / (self.height - 1)
        chart_width = self.width - 10  # Reserve space for y-axis labels
        
        # Normalize data to chart coordinates
        normalized_data = []
        for value in data:
            if data_max == data_min:
                norm_y = self.height // 2
            else:
                norm_y = int((value - data_min) / (data_max - data_min) * (self.height - 1))
            normalized_data.append(norm_y)
        
        # Create chart grid
        for y in range(self.height - 1, -1, -1):
            y_value = data_min + y * y_step
            y_label = f"{y_value:6.0f}" if abs(y_value) >= 1 else f"{y_value:6.2f}"
            
            line = f"{y_label} ┤"
            
            # Plot data points
            for x in range(chart_width):
                data_index = int(x * len(data) / chart_width)
                if data_index < len(normalized_data):
                    if normalized_data[data_index] == y:
                        # Connect points with lines
                        if x > 0:
                            prev_index = int((x-1) * len(data) / chart_width)
                            if prev_index < len(normalized_data):
                                prev_y = normalized_data[prev_index]
                                curr_y = normalized_data[data_index]
                                
                                if prev_y < curr_y and y > prev_y and y <= curr_y:
                                    line += "╱" if y == curr_y else "│"
                                elif prev_y > curr_y and y >= curr_y and y < prev_y:
                                    line += "╲" if y == curr_y else "│"
                                elif prev_y == curr_y and y == curr_y:
                                    line += "─"
                                else:
                                    line += " "
                            else:
                                line += "●"
                        else:
                            line += "●"
                    elif y < max(normalized_data) and any(
                        norm_y > y for norm_y in normalized_data[
                            max(0, int((x-1) * len(data) / chart_width)):
                            min(len(data), int((x+2) * len(data) / chart_width))
                        ]
                    ):
                        line += " "
                    else:
                        line += " "
                else:
                    line += " "
            
            chart_lines.append(line)
        
        # Add x-axis
        x_axis = "       └" + "─" * chart_width
        chart_lines.append(x_axis)
        
        # Add x-axis labels if provided
        if x_labels:
            label_line = "        "
            step = max(1, len(x_labels) // 5)  # Show max 5 labels
            for i in range(0, len(x_labels), step):
                pos = int(i * chart_width / len(x_labels))
                if pos < chart_width:
                    label_line += x_labels[i][:6].ljust(chart_width // 5)
            chart_lines.append(label_line)
        
        return "\n".join(chart_lines)
    
    def bar_chart(self, data: Dict[str, float], title: str = "",
                  max_bar_width: int = 30) -> str:
        """
        Generate ASCII bar chart
        
        Args:
            data: Dictionary of label -> value pairs
            title: Chart title
            max_bar_width: Maximum width of bars
        """
        if not data:
            return "No data to display"
        
        chart_lines = []
        
        # Add title
        if title:
            chart_lines.append(title)
            chart_lines.append("")
        
        max_value = max(data.values()) if data.values() else 1
        max_label_len = max(len(str(k)) for k in data.keys()) if data else 0
        
        for label, value in data.items():
            # Calculate bar length
            if max_value > 0:
                bar_length = int((value / max_value) * max_bar_width)
            else:
                bar_length = 0
            
            # Create bar
            bar = "█" * bar_length
            padding = " " * (max_bar_width - bar_length)
            
            # Format value
            value_str = f"{value:.1f}" if isinstance(value, float) else str(value)
            
            # Create line
            line = f"{label.ljust(max_label_len)} │{bar}{padding}│ {value_str}"
            chart_lines.append(line)
        
        return "\n".join(chart_lines)
    
    def sparkline(self, data: List[float], width: Optional[int] = None) -> str:
        """
        Generate compact sparkline chart
        
        Args:
            data: List of numeric values
            width: Width of sparkline (defaults to data length)
        """
        if not data:
            return ""
        
        width = width or len(data)
        if len(data) != width:
            # Resample data to fit width
            step = len(data) / width
            data = [data[int(i * step)] for i in range(width)]
        
        min_val, max_val = min(data), max(data)
        if min_val == max_val:
            return "─" * width
        
        # Map values to spark characters
        spark_chars = "▁▂▃▄▅▆▇█"
        result = ""
        
        for value in data:
            # Normalize to 0-7 range
            normalized = int((value - min_val) / (max_val - min_val) * 7)
            result += spark_chars[min(7, max(0, normalized))]
        
        return result
    
    def gauge(self, value: float, min_val: float = 0, max_val: float = 100,
              width: int = 20, label: str = "") -> str:
        """
        Generate ASCII gauge/progress bar
        
        Args:
            value: Current value
            min_val: Minimum possible value
            max_val: Maximum possible value
            width: Width of gauge
            label: Label for the gauge
        """
        # Normalize value
        if max_val == min_val:
            normalized = 0
        else:
            normalized = max(0, min(1, (value - min_val) / (max_val - min_val)))
        
        filled = int(normalized * width)
        empty = width - filled
        
        # Create gauge
        gauge_bar = "█" * filled + "░" * empty
        percentage = f"{normalized * 100:.0f}%"
        
        if label:
            return f"{label}: {gauge_bar} {percentage}"
        else:
            return f"{gauge_bar} {percentage}"
    
    def histogram(self, data: List[float], bins: int = 10, title: str = "") -> str:
        """
        Generate ASCII histogram
        
        Args:
            data: List of numeric values
            bins: Number of histogram bins
            title: Chart title
        """
        if not data:
            return "No data to display"
        
        chart_lines = []
        
        if title:
            chart_lines.append(title)
            chart_lines.append("")
        
        # Calculate histogram
        min_val, max_val = min(data), max(data)
        if min_val == max_val:
            max_val = min_val + 1
        
        bin_width = (max_val - min_val) / bins
        bin_counts = [0] * bins
        
        for value in data:
            bin_index = min(bins - 1, int((value - min_val) / bin_width))
            bin_counts[bin_index] += 1
        
        max_count = max(bin_counts) if bin_counts else 1
        
        # Generate bars
        for i, count in enumerate(bin_counts):
            bin_start = min_val + i * bin_width
            bin_end = min_val + (i + 1) * bin_width
            
            bar_height = int((count / max_count) * 10) if max_count > 0 else 0
            bar = "█" * bar_height + "░" * (10 - bar_height)
            
            range_str = f"[{bin_start:.1f}-{bin_end:.1f})"
            chart_lines.append(f"{range_str:12} │{bar}│ {count}")
        
        return "\n".join(chart_lines)


def create_time_series_chart(timestamps: List[datetime], values: List[float],
                           title: str = "", width: int = 60, height: int = 10) -> str:
    """
    Create time series chart with datetime x-axis
    
    Args:
        timestamps: List of datetime objects
        values: Corresponding values
        title: Chart title
        width: Chart width
        height: Chart height
    """
    if not timestamps or not values or len(timestamps) != len(values):
        return "Invalid time series data"
    
    chart = ASCIIChart(width, height)
    
    # Generate time labels
    time_labels = []
    if len(timestamps) > 0:
        start_time = timestamps[0]
        end_time = timestamps[-1]
        duration = end_time - start_time
        
        # Determine appropriate time format
        if duration.days > 1:
            time_labels = [ts.strftime("%m/%d") for ts in timestamps[::len(timestamps)//5]]
        elif duration.seconds > 3600:
            time_labels = [ts.strftime("%H:%M") for ts in timestamps[::len(timestamps)//5]]
        else:
            time_labels = [ts.strftime("%H:%M:%S") for ts in timestamps[::len(timestamps)//5]]
    
    return chart.line_chart(values, title=title, x_labels=time_labels)


def create_system_overview_chart(metrics: Dict[str, float]) -> str:
    """
    Create system overview with multiple gauges
    
    Args:
        metrics: Dictionary of metric_name -> value pairs
    """
    chart = ASCIIChart()
    lines = []
    
    # Common system metrics with their typical ranges
    metric_ranges = {
        'cpu': (0, 100),
        'memory': (0, 100),
        'disk': (0, 100),
        'load': (0, 4),
        'temperature': (0, 100)
    }
    
    for metric, value in metrics.items():
        min_val, max_val = metric_ranges.get(metric.lower(), (0, 100))
        gauge_line = chart.gauge(value, min_val, max_val, width=20, 
                               label=metric.capitalize())
        lines.append(gauge_line)
    
    return "\n".join(lines)


# Color helpers for terminal output
class Colors:
    """ANSI color codes for terminal output"""
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'
    END = '\033[0m'  # Reset
    
    @classmethod
    def colorize(cls, text: str, color: str) -> str:
        """Apply color to text"""
        return f"{color}{text}{cls.END}"
    
    @classmethod
    def status_color(cls, value: float, warning_threshold: float = 70,
                    critical_threshold: float = 90) -> str:
        """Return appropriate color based on status value"""
        if value >= critical_threshold:
            return cls.RED
        elif value >= warning_threshold:
            return cls.YELLOW
        else:
            return cls.GREEN