Metadata-Version: 2.1
Name: mdit-py-plugins
Version: 0.4.2
Summary: Collection of plugins for markdown-it-py
Keywords: markdown,markdown-it,lexer,parser,development
Author-email: <PERSON> <<EMAIL>>
Requires-Python: >=3.8
Description-Content-Type: text/markdown
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Topic :: Text Processing :: Markup
Requires-Dist: markdown-it-py>=1.0.0,<4.0.0
Requires-Dist: pre-commit ; extra == "code_style"
Requires-Dist: myst-parser ; extra == "rtd"
Requires-Dist: sphinx-book-theme ; extra == "rtd"
Requires-Dist: coverage ; extra == "testing"
Requires-Dist: pytest ; extra == "testing"
Requires-Dist: pytest-cov ; extra == "testing"
Requires-Dist: pytest-regressions ; extra == "testing"
Project-URL: Documentation, https://mdit-py-plugins.readthedocs.io
Project-URL: Homepage, https://github.com/executablebooks/mdit-py-plugins
Provides-Extra: code_style
Provides-Extra: rtd
Provides-Extra: testing

# mdit-py-plugins

[![Github-CI][github-ci]][github-link]
[![Coverage Status][codecov-badge]][codecov-link]
[![PyPI][pypi-badge]][pypi-link]
[![Conda][conda-badge]][conda-link]
[![Code style: black][black-badge]][black-link]

Collection of core plugins for [markdown-it-py](https://github.com/executablebooks/markdown-it-py).

[github-ci]: https://github.com/executablebooks/mdit-py-plugins/workflows/continuous-integration/badge.svg
[github-link]: https://github.com/executablebooks/mdit-py-plugins
[pypi-badge]: https://img.shields.io/pypi/v/mdit-py-plugins.svg
[pypi-link]: https://pypi.org/project/mdit-py-plugins
[conda-badge]: https://anaconda.org/conda-forge/mdit-py-plugins/badges/version.svg
[conda-link]: https://anaconda.org/conda-forge/mdit-py-plugins
[codecov-badge]: https://codecov.io/gh/executablebooks/mdit-py-plugins/branch/master/graph/badge.svg
[codecov-link]: https://codecov.io/gh/executablebooks/mdit-py-plugins
[black-badge]: https://img.shields.io/badge/code%20style-black-000000.svg
[black-link]: https://github.com/ambv/black
[install-badge]: https://img.shields.io/pypi/dw/mdit-py-plugins?label=pypi%20installs
[install-link]: https://pypistats.org/packages/mdit-py-plugins

