"""
Data Formatting Utilities for Autonomous System Manager
Provides consistent data formatting and display functions
"""

import json
import csv
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union, Tuple
from io import StringIO
import math


class DataFormatter:
    """Handles formatting of various data types for console display"""
    
    @staticmethod
    def format_bytes(bytes_value: Union[int, float], precision: int = 1) -> str:
        """
        Format bytes into human-readable format
        
        Args:
            bytes_value: Size in bytes
            precision: Decimal places to show
        
        Returns:
            Formatted string (e.g., "1.5 GB", "512 MB")
        """
        if bytes_value == 0:
            return "0 B"
        
        units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB']
        unit_index = 0
        size = float(bytes_value)
        
        while size >= 1024.0 and unit_index < len(units) - 1:
            size /= 1024.0
            unit_index += 1
        
        if unit_index == 0:  # Bytes
            return f"{int(size)} {units[unit_index]}"
        else:
            return f"{size:.{precision}f} {units[unit_index]}"
    
    @staticmethod
    def format_percentage(value: float, precision: int = 1) -> str:
        """
        Format decimal as percentage
        
        Args:
            value: Decimal value (0.0 to 1.0 or 0 to 100)
            precision: Decimal places to show
        
        Returns:
            Formatted percentage string
        """
        # Handle both 0-1 and 0-100 ranges
        if value <= 1.0:
            percentage = value * 100
        else:
            percentage = value
        
        return f"{percentage:.{precision}f}%"
    
    @staticmethod
    def format_duration(seconds: Union[int, float]) -> str:
        """
        Format seconds into human-readable duration
        
        Args:
            seconds: Duration in seconds
        
        Returns:
            Formatted duration (e.g., "2h 30m", "45s")
        """
        if seconds < 0:
            return "0s"
        
        seconds = int(seconds)
        
        if seconds < 60:
            return f"{seconds}s"
        elif seconds < 3600:
            minutes = seconds // 60
            remaining_seconds = seconds % 60
            if remaining_seconds > 0:
                return f"{minutes}m {remaining_seconds}s"
            return f"{minutes}m"
        elif seconds < 86400:
            hours = seconds // 3600
            remaining_minutes = (seconds % 3600) // 60
            if remaining_minutes > 0:
                return f"{hours}h {remaining_minutes}m"
            return f"{hours}h"
        else:
            days = seconds // 86400
            remaining_hours = (seconds % 86400) // 3600
            if remaining_hours > 0:
                return f"{days}d {remaining_hours}h"
            return f"{days}d"
    
    @staticmethod
    def format_timestamp(timestamp: Union[datetime, float, int], 
                        format_type: str = "default") -> str:
        """
        Format timestamp for display
        
        Args:
            timestamp: datetime object, unix timestamp, or formatted string
            format_type: Type of formatting ("default", "short", "long", "iso")
        
        Returns:
            Formatted timestamp string
        """
        if isinstance(timestamp, (int, float)):
            dt = datetime.fromtimestamp(timestamp)
        elif isinstance(timestamp, datetime):
            dt = timestamp
        else:
            return str(timestamp)
        
        formats = {
            "default": "%Y-%m-%d %H:%M:%S",
            "short": "%m/%d %H:%M",
            "long": "%A, %B %d, %Y at %I:%M:%S %p",
            "iso": "%Y-%m-%dT%H:%M:%S",
            "time_only": "%H:%M:%S",
            "date_only": "%Y-%m-%d"
        }
        
        return dt.strftime(formats.get(format_type, formats["default"]))
    
    @staticmethod
    def format_number(number: Union[int, float], precision: int = 2, 
                     thousands_sep: bool = True) -> str:
        """
        Format numbers with optional thousands separator
        
        Args:
            number: Number to format
            precision: Decimal places for floats
            thousands_sep: Whether to include thousands separator
        
        Returns:
            Formatted number string
        """
        if isinstance(number, int):
            if thousands_sep:
                return f"{number:,}"
            return str(number)
        else:
            if thousands_sep:
                return f"{number:,.{precision}f}"
            return f"{number:.{precision}f}"
    
    @staticmethod
    def format_cpu_usage(usage: float) -> str:
        """Format CPU usage with appropriate precision and units"""
        return f"{usage:.1f}%"
    
    @staticmethod
    def format_memory_info(used: int, total: int) -> str:
        """Format memory usage information"""
        used_str = DataFormatter.format_bytes(used)
        total_str = DataFormatter.format_bytes(total)
        percentage = (used / total * 100) if total > 0 else 0
        return f"{used_str} / {total_str} ({percentage:.1f}%)"
    
    @staticmethod
    def format_network_speed(bytes_per_sec: float) -> str:
        """Format network speed"""
        return f"{DataFormatter.format_bytes(bytes_per_sec)}/s"


class TableFormatter:
    """Handles table formatting for console display"""
    
    @staticmethod
    def create_table(data: List[Dict[str, Any]], headers: Optional[List[str]] = None,
                    max_width: Optional[int] = None, align: str = "left") -> str:
        """
        Create formatted table from list of dictionaries
        
        Args:
            data: List of dictionaries with table data
            headers: Custom headers (defaults to dict keys)
            max_width: Maximum table width
            align: Text alignment ("left", "right", "center")
        
        Returns:
            Formatted table string
        """
        if not data:
            return "No data to display"
        
        # Get headers
        if headers is None:
            headers = list(data[0].keys())
        
        # Calculate column widths
        col_widths = {}
        for header in headers:
            col_widths[header] = len(str(header))
            for row in data:
                value = str(row.get(header, ""))
                col_widths[header] = max(col_widths[header], len(value))
        
        # Apply max width constraint if specified
        if max_width:
            total_width = sum(col_widths.values()) + len(headers) * 3 + 1
            if total_width > max_width:
                # Proportionally reduce column widths
                reduction_factor = (max_width - len(headers) * 3 - 1) / sum(col_widths.values())
                for header in headers:
                    col_widths[header] = max(8, int(col_widths[header] * reduction_factor))
        
        # Build table
        lines = []
        
        # Header separator
        separator = "+" + "+".join("-" * (col_widths[h] + 2) for h in headers) + "+"
        lines.append(separator)
        
        # Header row
        header_row = "|"
        for header in headers:
            formatted_header = TableFormatter._align_text(str(header), col_widths[header], align)
            header_row += f" {formatted_header} |"
        lines.append(header_row)
        lines.append(separator)
        
        # Data rows
        for row in data:
            data_row = "|"
            for header in headers:
                value = str(row.get(header, ""))
                # Truncate if too long
                if len(value) > col_widths[header]:
                    value = value[:col_widths[header]-3] + "..."
                formatted_value = TableFormatter._align_text(value, col_widths[header], align)
                data_row += f" {formatted_value} |"
            lines.append(data_row)
        
        # Bottom separator
        lines.append(separator)
        
        return "\n".join(lines)
    
    @staticmethod
    def create_simple_table(data: List[List[str]], headers: Optional[List[str]] = None) -> str:
        """
        Create simple table without borders
        
        Args:
            data: List of lists with table data
            headers: Optional headers
        
        Returns:
            Simple formatted table
        """
        if not data:
            return "No data to display"
        
        all_rows = []
        if headers:
            all_rows.append(headers)
        all_rows.extend(data)
        
        # Calculate column widths
        col_widths = [0] * len(all_rows[0])
        for row in all_rows:
            for i, cell in enumerate(row):
                if i < len(col_widths):
                    col_widths[i] = max(col_widths[i], len(str(cell)))
        
        # Format rows
        lines = []
        for i, row in enumerate(all_rows):
            formatted_row = []
            for j, cell in enumerate(row):
                if j < len(col_widths):
                    formatted_row.append(str(cell).ljust(col_widths[j]))
            lines.append("  ".join(formatted_row))
            
            # Add separator after header
            if i == 0 and headers:
                separator = "  ".join("-" * width for width in col_widths)
                lines.append(separator)
        
        return "\n".join(lines)
    
    @staticmethod
    def _align_text(text: str, width: int, align: str) -> str:
        """Align text within specified width"""
        if len(text) >= width:
            return text[:width]
        
        if align == "right":
            return text.rjust(width)
        elif align == "center":
            return text.center(width)
        else:  # left
            return text.ljust(width)


class ReportFormatter:
    """Handles report generation and formatting"""
    
    @staticmethod
    def create_system_report(metrics: Dict[str, Any], alerts: List[Dict[str, Any]],
                           actions: List[Dict[str, Any]], timeframe: str = "24h") -> str:
        """
        Create comprehensive system report
        
        Args:
            metrics: Current system metrics
            alerts: Recent alerts
            actions: Recent actions
            timeframe: Report timeframe
        
        Returns:
            Formatted report string
        """
        report_lines = []
        current_time = datetime.now()
        
        # Header
        report_lines.extend([
            "=" * 60,
            f"SYSTEM HEALTH REPORT - {timeframe.upper()}",
            f"Generated: {DataFormatter.format_timestamp(current_time)}",
            "=" * 60,
            ""
        ])
        
        # System Overview
        report_lines.extend([
            "SYSTEM OVERVIEW",
            "-" * 20,
            ""
        ])
        
        if metrics:
            for key, value in metrics.items():
                if isinstance(value, (int, float)):
                    if 'memory' in key.lower() or 'disk' in key.lower():
                        formatted_value = DataFormatter.format_bytes(value)
                    elif 'cpu' in key.lower() or 'usage' in key.lower():
                        formatted_value = DataFormatter.format_percentage(value)
                    else:
                        formatted_value = DataFormatter.format_number(value)
                else:
                    formatted_value = str(value)
                
                report_lines.append(f"{key.replace('_', ' ').title()}: {formatted_value}")
        
        report_lines.append("")
        
        # Recent Alerts
        report_lines.extend([
            f"RECENT ALERTS ({len(alerts)})",
            "-" * 20,
            ""
        ])
        
        if alerts:
            for alert in alerts[-10:]:  # Show last 10 alerts
                timestamp = alert.get('timestamp', current_time)
                severity = alert.get('severity', 'INFO')
                message = alert.get('message', 'No message')
                
                time_str = DataFormatter.format_timestamp(timestamp, "short")
                report_lines.append(f"[{time_str}] {severity}: {message}")
        else:
            report_lines.append("No recent alerts")
        
        report_lines.append("")
        
        # Recent Actions
        report_lines.extend([
            f"RECENT ACTIONS ({len(actions)})",
            "-" * 20,
            ""
        ])
        
        if actions:
            for action in actions[-10:]:  # Show last 10 actions
                timestamp = action.get('timestamp', current_time)
                action_type = action.get('type', 'Unknown')
                status = action.get('status', 'Unknown')
                
                time_str = DataFormatter.format_timestamp(timestamp, "short")
                report_lines.append(f"[{time_str}] {action_type}: {status}")
        else:
            report_lines.append("No recent actions")
        
        report_lines.extend([
            "",
            "=" * 60,
            f"Report generated by Autonomous System Manager",
            "=" * 60
        ])
        
        return "\n".join(report_lines)
    
    @staticmethod
    def export_to_csv(data: List[Dict[str, Any]], filename: str = None) -> str:
        """
        Export data to CSV format
        
        Args:
            data: List of dictionaries to export
            filename: Optional filename for saving
        
        Returns:
            CSV content as string
        """
        if not data:
            return ""
        
        output = StringIO()
        writer = csv.DictWriter(output, fieldnames=data[0].keys())
        writer.writeheader()
        writer.writerows(data)
        
        csv_content = output.getvalue()
        output.close()
        
        if filename:
            with open(filename, 'w', newline='') as f:
                f.write(csv_content)
        
        return csv_content
    
    @staticmethod
    def export_to_json(data: Any, filename: str = None, indent: int = 2) -> str:
        """
        Export data to JSON format
        
        Args:
            data: Data to export
            filename: Optional filename for saving
            indent: JSON indentation
        
        Returns:
            JSON content as string
        """
        # Handle datetime objects
        def json_serializer(obj):
            if isinstance(obj, datetime):
                return obj.isoformat()
            raise TypeError(f"Object of type {type(obj)} is not JSON serializable")
        
        json_content = json.dumps(data, indent=indent, default=json_serializer, 
                                ensure_ascii=False)
        
        if filename:
            with open(filename, 'w') as f:
                f.write(json_content)
        
        return json_content


class StatusFormatter:
    """Handles status indicators and health formatting"""
    
    STATUS_ICONS = {
        'good': '✅',
        'warning': '⚠️',
        'critical': '❌',
        'info': 'ℹ️',
        'unknown': '❓'
    }
    
    @staticmethod
    def get_status_indicator(value: float, warning_threshold: float = 70,
                           critical_threshold: float = 90) -> Tuple[str, str]:
        """
        Get status indicator and level based on value
        
        Args:
            value: Metric value
            warning_threshold: Warning threshold
            critical_threshold: Critical threshold
        
        Returns:
            Tuple of (icon, status_level)
        """
        if value >= critical_threshold:
            return StatusFormatter.STATUS_ICONS['critical'], 'CRITICAL'
        elif value >= warning_threshold:
            return StatusFormatter.STATUS_ICONS['warning'], 'WARNING'
        else:
            return StatusFormatter.STATUS_ICONS['good'], 'GOOD'
    
    @staticmethod
    def format_health_status(metrics: Dict[str, float], thresholds: Dict[str, Tuple[float, float]] = None) -> str:
        """
        Format overall system health status
        
        Args:
            metrics: Dictionary of metric_name -> value
            thresholds: Dictionary of metric_name -> (warning, critical) thresholds
        
        Returns:
            Formatted health status string
        """
        if not metrics:
            return "No metrics available"
        
        default_thresholds = {
            'cpu': (70, 90),
            'memory': (80, 95),
            'disk': (85, 95),
            'load': (2, 4),
            'temperature': (70, 85)
        }
        
        if thresholds:
            default_thresholds.update(thresholds)
        
        status_lines = []
        overall_status = 'GOOD'
        
        for metric, value in metrics.items():
            metric_key = metric.lower()
            warning_thresh, critical_thresh = default_thresholds.get(metric_key, (70, 90))
            
            icon, status = StatusFormatter.get_status_indicator(value, warning_thresh, critical_thresh)
            formatted_value = DataFormatter.format_percentage(value) if value <= 100 else DataFormatter.format_number(value)
            
            status_lines.append(f"{icon} {metric.capitalize()}: {formatted_value} ({status})")
            
            # Update overall status
            if status == 'CRITICAL' and overall_status != 'CRITICAL':
                overall_status = 'CRITICAL'
            elif status == 'WARNING' and overall_status == 'GOOD':
                overall_status = 'WARNING'
        
        # Add overall status header
        overall_icon = StatusFormatter.STATUS_ICONS.get(overall_status.lower(), '❓')
        header = f"{overall_icon} OVERALL SYSTEM STATUS: {overall_status}\n" + "=" * 40 + "\n"
        
        return header + "\n".join(status_lines)
    
    @staticmethod
    def format_alert_summary(alerts: List[Dict[str, Any]], max_alerts: int = 5) -> str:
        """
        Format recent alerts summary
        
        Args:
            alerts: List of alert dictionaries
            max_alerts: Maximum number of alerts to show
        
        Returns:
            Formatted alerts summary
        """
        if not alerts:
            return f"{StatusFormatter.STATUS_ICONS['good']} No recent alerts"
        
        lines = [f"🚨 RECENT ALERTS ({len(alerts)} total)"]
        lines.append("-" * 30)
        
        # Show most recent alerts
        recent_alerts = alerts[-max_alerts:] if len(alerts) > max_alerts else alerts
        
        for alert in reversed(recent_alerts):  # Most recent first
            timestamp = alert.get('timestamp', datetime.now())
            severity = alert.get('severity', 'INFO').upper()
            message = alert.get('message', 'No message')
            
            time_str = DataFormatter.format_timestamp(timestamp, "time_only")
            
            # Get severity icon
            severity_icons = {
                'CRITICAL': '🔴',
                'WARNING': '🟡',
                'INFO': '🔵',
                'DEBUG': '⚪'
            }
            
            icon = severity_icons.get(severity, '❓')
            lines.append(f"{icon} [{time_str}] {message}")
        
        if len(alerts) > max_alerts:
            lines.append(f"... and {len(alerts) - max_alerts} more alerts")
        
        return "\n".join(lines)


class ProgressFormatter:
    """Handles progress bars and loading indicators"""
    
    @staticmethod
    def create_progress_bar(current: int, total: int, width: int = 30, 
                          show_percentage: bool = True, char: str = "█") -> str:
        """
        Create ASCII progress bar
        
        Args:
            current: Current progress value
            total: Total/maximum value
            width: Width of progress bar
            show_percentage: Whether to show percentage
            char: Character to use for filled portion
        
        Returns:
            Formatted progress bar string
        """
        if total <= 0:
            return "Invalid progress values"
        
        percentage = min(100, (current / total) * 100)
        filled_width = int((current / total) * width)
        empty_width = width - filled_width
        
        bar = char * filled_width + "░" * empty_width
        
        if show_percentage:
            return f"[{bar}] {percentage:.1f}% ({current}/{total})"
        else:
            return f"[{bar}] ({current}/{total})"
    
    @staticmethod
    def create_loading_spinner(step: int) -> str:
        """
        Create rotating loading spinner
        
        Args:
            step: Current animation step
        
        Returns:
            Spinner character
        """
        spinners = ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏']
        return spinners[step % len(spinners)]
    
    @staticmethod
    def create_multi_progress(items: List[Tuple[str, int, int]], width: int = 25) -> str:
        """
        Create multiple progress bars
        
        Args:
            items: List of (label, current, total) tuples
            width: Width of each progress bar
        
        Returns:
            Multi-line progress display
        """
        lines = []
        max_label_width = max(len(item[0]) for item in items) if items else 0
        
        for label, current, total in items:
            padded_label = label.ljust(max_label_width)
            progress_bar = ProgressFormatter.create_progress_bar(current, total, width)
            lines.append(f"{padded_label}: {progress_bar}")
        
        return "\n".join(lines)


class LogFormatter:
    """Handles log message formatting"""
    
    LOG_LEVELS = {
        'DEBUG': '🔍',
        'INFO': 'ℹ️',
        'WARNING': '⚠️',
        'ERROR': '❌',
        'CRITICAL': '🚨'
    }
    
    @staticmethod
    def format_log_entry(timestamp: datetime, level: str, message: str, 
                        source: str = None) -> str:
        """
        Format log entry for console display
        
        Args:
            timestamp: Log timestamp
            level: Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
            message: Log message
            source: Optional source identifier
        
        Returns:
            Formatted log entry
        """
        time_str = DataFormatter.format_timestamp(timestamp, "default")
        level_icon = LogFormatter.LOG_LEVELS.get(level.upper(), '❓')
        
        if source:
            return f"[{time_str}] {level_icon} {level.upper()} [{source}]: {message}"
        else:
            return f"[{time_str}] {level_icon} {level.upper()}: {message}"
    
    @staticmethod
    def format_log_summary(logs: List[Dict[str, Any]], max_lines: int = 20) -> str:
        """
        Format recent logs summary
        
        Args:
            logs: List of log dictionaries
            max_lines: Maximum number of log lines to show
        
        Returns:
            Formatted log summary
        """
        if not logs:
            return "No recent log entries"
        
        lines = ["📋 RECENT LOG ENTRIES"]
        lines.append("=" * 25)
        
        recent_logs = logs[-max_lines:] if len(logs) > max_lines else logs
        
        for log_entry in reversed(recent_logs):  # Most recent first
            timestamp = log_entry.get('timestamp', datetime.now())
            level = log_entry.get('level', 'INFO')
            message = log_entry.get('message', 'No message')
            source = log_entry.get('source')
            
            formatted_entry = LogFormatter.format_log_entry(timestamp, level, message, source)
            lines.append(formatted_entry)
        
        if len(logs) > max_lines:
            lines.append(f"... and {len(logs) - max_lines} more entries")
        
        return "\n".join(lines)


# Utility functions for common formatting tasks

def format_uptime(uptime_seconds: float) -> str:
    """Format system uptime"""
    return f"Uptime: {DataFormatter.format_duration(uptime_seconds)}"


def format_process_info(processes: List[Dict[str, Any]], max_processes: int = 10) -> str:
    """Format process information table"""
    if not processes:
        return "No process information available"
    
    # Sort by CPU usage (descending)
    sorted_processes = sorted(processes, key=lambda p: p.get('cpu_percent', 0), reverse=True)
    top_processes = sorted_processes[:max_processes]
    
    # Prepare table data
    table_data = []
    for proc in top_processes:
        table_data.append({
            'PID': proc.get('pid', 'N/A'),
            'Name': proc.get('name', 'Unknown')[:15],  # Truncate long names
            'CPU%': f"{proc.get('cpu_percent', 0):.1f}%",
            'Memory': DataFormatter.format_bytes(proc.get('memory_info', {}).get('rss', 0)),
            'Status': proc.get('status', 'Unknown')
        })
    
    return TableFormatter.create_table(table_data)


def format_disk_usage(disk_info: List[Dict[str, Any]]) -> str:
    """Format disk usage information"""
    if not disk_info:
        return "No disk information available"
    
    table_data = []
    for disk in disk_info:
        mountpoint = disk.get('mountpoint', '/')
        total = disk.get('total', 0)
        used = disk.get('used', 0)
        free = disk.get('free', 0)
        percent = disk.get('percent', 0)
        
        table_data.append({
            'Mount': mountpoint,
            'Total': DataFormatter.format_bytes(total),
            'Used': DataFormatter.format_bytes(used),
            'Free': DataFormatter.format_bytes(free),
            'Use%': f"{percent:.1f}%"
        })
    
    return TableFormatter.create_table(table_data)


def format_network_stats(network_info: Dict[str, Any]) -> str:
    """Format network statistics"""
    if not network_info:
        return "No network information available"
    
    lines = ["🌐 NETWORK STATISTICS"]
    lines.append("-" * 25)
    
    for interface, stats in network_info.items():
        if interface.startswith('lo'):  # Skip loopback
            continue
        
        bytes_sent = stats.get('bytes_sent', 0)
        bytes_recv = stats.get('bytes_recv', 0)
        packets_sent = stats.get('packets_sent', 0)
        packets_recv = stats.get('packets_recv', 0)
        
        lines.extend([
            f"Interface: {interface}",
            f"  Sent: {DataFormatter.format_bytes(bytes_sent)} ({DataFormatter.format_number(packets_sent)} packets)",
            f"  Received: {DataFormatter.format_bytes(bytes_recv)} ({DataFormatter.format_number(packets_recv)} packets)",
            ""
        ])
    
    return "\n".join(lines)


# Export commonly used classes and functions
__all__ = [
    'DataFormatter',
    'TableFormatter', 
    'ReportFormatter',
    'StatusFormatter',
    'ProgressFormatter',
    'LogFormatter',
    'format_uptime',
    'format_process_info',
    'format_disk_usage', 
    'format_network_stats'
]