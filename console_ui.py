import os
import time
import signal
import sys
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable
import threading
from collections import deque

# Rich library for beautiful console output
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.table import Table
from rich.layout import Layout
from rich.live import Live
from rich.text import Text
from rich.prompt import Prompt, Confirm
from rich.align import Align
from rich.columns import Columns
from rich.rule import Rule

# ASCII charts
import plotext as plt

# For keyboard input handling
import keyboard
import threading

class ConsoleUI:
    """Interactive console interface for the autonomous system manager."""
    
    def __init__(self, database_manager, system_monitor=None, action_engine=None):
        self.console = Console()
        self.db = database_manager
        self.monitor = system_monitor
        self.actions = action_engine
        self.running = False
        self.refresh_rate = 5  # seconds
        self.current_mode = "dashboard"
        self.selected_menu_item = 0
        self.show_help = False
        
        # Recent data cache for performance
        self.recent_metrics = deque(maxlen=100)
        self.recent_alerts = deque(maxlen=50)
        self.recent_actions = deque(maxlen=50)
        
        # Color schemes
        self.colors = {
            'normal': 'green',
            'warning': 'yellow',
            'critical': 'red',
            'info': 'blue',
            'disabled': 'dim white'
        }
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully."""
        self.running = False
        self.console.print("\n[yellow]Shutting down console interface...[/yellow]")
        sys.exit(0)
    
    def _get_status_color(self, value: float, thresholds: Dict) -> str:
        """Determine color based on value and thresholds."""
        if value >= thresholds.get('critical', 90):
            return self.colors['critical']
        elif value >= thresholds.get('warning', 80):
            return self.colors['warning']
        else:
            return self.colors['normal']
    
    def _create_progress_bar(self, value: float, max_value: float = 100, 
                           width: int = 20) -> str:
        """Create a text-based progress bar."""
        if max_value == 0:
            percentage = 0
        else:
            percentage = min(value / max_value, 1.0)
        
        filled = int(percentage * width)
        bar = '█' * filled + '░' * (width - filled)
        return f"{bar} {value:.1f}%"
    
    def _create_sparkline(self, data: List[float], width: int = 20) -> str:
        """Create a simple ASCII sparkline."""
        if not data:
            return '─' * width
        
        min_val, max_val = min(data), max(data)
        if min_val == max_val:
            return '─' * width
        
        # Unicode characters for sparkline
        chars = ['▁', '▂', '▃', '▄', '▅', '▆', '▇', '█']
        
        sparkline = ""
        for i in range(min(width, len(data))):
            if i < len(data):
                normalized = (data[i] - min_val) / (max_val - min_val)
                char_index = int(normalized * (len(chars) - 1))
                sparkline += chars[char_index]
            else:
                sparkline += '▁'
        
        return sparkline
    
    def _get_system_metrics(self) -> Dict:
        """Get current system metrics."""
        try:
            # Get latest metrics from database
            recent_metrics = self.db.get_recent_metrics(hours=1, limit=10)
            
            # Organize by metric type
            metrics = {}
            for metric in recent_metrics:
                metric_type = metric['metric_type']
                if metric_type not in metrics:
                    metrics[metric_type] = []
                metrics[metric_type].append(metric['value'])
            
            # Calculate current values (latest)
            current = {}
            for metric_type, values in metrics.items():
                if values:
                    current[metric_type] = {
                        'current': values[0],
                        'trend': values[:10],  # Last 10 values for trend
                        'avg': sum(values) / len(values)
                    }
            
            return current
            
        except Exception as e:
            self.console.print(f"[red]Error getting metrics: {e}[/red]")
            return {}
    
    def _create_dashboard_layout(self) -> Layout:
        """Create the main dashboard layout."""
        layout = Layout()
        
        # Split into header, body, and footer
        layout.split_column(
            Layout(name="header", size=3),
            Layout(name="body"),
            Layout(name="footer", size=3)
        )
        
        # Split body into left and right panels
        layout["body"].split_row(
            Layout(name="metrics", ratio=2),
            Layout(name="alerts", ratio=1)
        )
        
        return layout
    
    def _update_dashboard_content(self, layout: Layout):
        """Update dashboard content with current data."""
        # Header
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        header_text = f"[bold blue]System Monitor Dashboard[/bold blue] - {current_time}"
        layout["header"].update(Panel(Align.center(header_text)))
        
        # Get current metrics
        metrics = self._get_system_metrics()
        
        # Create metrics panel
        metrics_table = Table(title="System Metrics", show_header=True, header_style="bold magenta")
        metrics_table.add_column("Metric", style="cyan", width=15)
        metrics_table.add_column("Current", width=10)
        metrics_table.add_column("Status", width=12)
        metrics_table.add_column("Trend", width=25)
        
        # Define thresholds
        thresholds = {
            'cpu_percent': {'warning': 70, 'critical': 85},
            'memory_percent': {'warning': 80, 'critical': 90},
            'disk_percent': {'warning': 80, 'critical': 95}
        }
        
        for metric_type, data in metrics.items():
            if metric_type in thresholds:
                current_val = data['current']
                color = self._get_status_color(current_val, thresholds[metric_type])
                
                # Create progress bar
                progress_bar = self._create_progress_bar(current_val)
                
                # Create trend sparkline
                trend = self._create_sparkline(list(reversed(data['trend'])))
                
                # Status text
                if current_val >= thresholds[metric_type]['critical']:
                    status = f"[{color}]CRITICAL[/{color}]"
                elif current_val >= thresholds[metric_type]['warning']:
                    status = f"[{color}]WARNING[/{color}]"
                else:
                    status = f"[{color}]NORMAL[/{color}]"
                
                metrics_table.add_row(
                    metric_type.replace('_', ' ').title(),
                    f"[{color}]{current_val:.1f}%[/{color}]",
                    status,
                    f"{trend} {data['avg']:.1f}% avg"
                )
        
        layout["metrics"].update(Panel(metrics_table))
        
        # Create alerts panel
        recent_alerts = self.db.get_recent_alerts(hours=24, limit=10)
        
        alerts_table = Table(title="Recent Alerts", show_header=True, header_style="bold red")
        alerts_table.add_column("Time", width=12)
        alerts_table.add_column("Type", width=15)
        alerts_table.add_column("Message", width=30)
        
        for alert in recent_alerts[:5]:  # Show only 5 most recent
            timestamp = datetime.fromisoformat(alert['timestamp']).strftime("%H:%M:%S")
            severity_color = self.colors.get(alert['severity'].lower(), 'white')
            
            alerts_table.add_row(
                timestamp,
                f"[{severity_color}]{alert['severity']}[/{severity_color}]",
                alert['message'][:30] + "..." if len(alert['message']) > 30 else alert['message']
            )
        
        if not recent_alerts:
            alerts_table.add_row("--", "No alerts", "System running normally")
        
        layout["alerts"].update(Panel(alerts_table))
        
        # Footer with controls
        footer_text = "[bold]Controls:[/bold] [cyan]Q[/cyan]uit | [cyan]R[/cyan]efresh | [cyan]A[/cyan]lerts | [cyan]C[/cyan]onfig | [cyan]H[/cyan]elp"
        layout["footer"].update(Panel(Align.center(footer_text)))
    
    def _show_alerts_view(self):
        """Display detailed alerts view."""
        self.console.clear()
        
        # Get alerts
        alerts = self.db.get_recent_alerts(hours=24, limit=20)
        
        table = Table(title="System Alerts (Last 24 Hours)", show_header=True)
        table.add_column("ID", width=8)
        table.add_column("Timestamp", width=19)
        table.add_column("Severity", width=10)
        table.add_column("Type", width=15)
        table.add_column("Message", width=40)
        table.add_column("Status", width=10)
        
        for alert in alerts:
            timestamp = datetime.fromisoformat(alert['timestamp']).strftime("%m-%d %H:%M:%S")
            severity_color = self.colors.get(alert['severity'].lower(), 'white')
            status = "Resolved" if alert['resolved'] else "Active"
            status_color = 'green' if alert['resolved'] else 'red'
            
            table.add_row(
                str(alert['id']),
                timestamp,
                f"[{severity_color}]{alert['severity']}[/{severity_color}]",
                alert['alert_type'],
                alert['message'],
                f"[{status_color}]{status}[/{status_color}]"
            )
        
        if not alerts:
            table.add_row("--", "--", "No alerts", "System OK", "All systems normal", "Active")
        
        self.console.print(table)
        
        # Show options
        self.console.print("\n[bold]Options:[/bold]")
        self.console.print("1. Resolve alert by ID")
        self.console.print("2. Export alerts to CSV")
        self.console.print("3. Back to dashboard")
        
        choice = Prompt.ask("Select option", choices=["1", "2", "3"], default="3")
        
        if choice == "1":
            self._resolve_alert_interactive()
        elif choice == "2":
            self._export_alerts()
        # Choice 3 returns to dashboard automatically
    
    def _resolve_alert_interactive(self):
        """Interactive alert resolution."""
        alert_id = Prompt.ask("Enter alert ID to resolve", default="")
        
        if alert_id.isdigit():
            success = self.db.resolve_alert(int(alert_id))
            if success:
                self.console.print(f"[green]Alert {alert_id} resolved successfully[/green]")
            else:
                self.console.print(f"[red]Failed to resolve alert {alert_id}[/red]")
        else:
            self.console.print("[red]Invalid alert ID[/red]")
        
        input("Press Enter to continue...")
    
    def _export_alerts(self):
        """Export alerts to CSV file."""
        filename = f"alerts_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        
        if self.db.export_data('alerts', filename, days=7):
            self.console.print(f"[green]Alerts exported to {filename}[/green]")
        else:
            self.console.print("[red]Failed to export alerts[/red]")
        
        input("Press Enter to continue...")
    
    def _show_actions_view(self):
        """Display recent actions view."""
        self.console.clear()
        
        actions = self.db.get_recent_actions(hours=24, limit=15)
        
        table = Table(title="Recent Actions (Last 24 Hours)", show_header=True)
        table.add_column("ID", width=8)
        table.add_column("Timestamp", width=19)
        table.add_column("Action", width=20)
        table.add_column("Description", width=35)
        table.add_column("Status", width=10)
        table.add_column("Duration", width=10)
        
        for action in actions:
            timestamp = datetime.fromisoformat(action['timestamp']).strftime("%m-%d %H:%M:%S")
            status_color = 'green' if action['status'] == 'SUCCESS' else 'red'
            duration = f"{action['execution_time']:.2f}s" if action['execution_time'] else "--"
            
            table.add_row(
                str(action['id']),
                timestamp,
                action['action_type'],
                action['description'][:35] + "..." if len(action['description']) > 35 else action['description'],
                f"[{status_color}]{action['status']}[/{status_color}]",
                duration
            )
        
        if not actions:
            table.add_row("--", "--", "No actions", "No automated actions taken", "N/A", "--")
        
        self.console.print(table)
        
        # Show options
        self.console.print("\n[bold]Options:[/bold]")
        self.console.print("1. View action details")
        self.console.print("2. Export actions to CSV")
        self.console.print("3. Manual action menu")
        self.console.print("4. Back to dashboard")
        
        choice = Prompt.ask("Select option", choices=["1", "2", "3", "4"], default="4")
        
        if choice == "1":
            self._view_action_details()
        elif choice == "2":
            self._export_actions()
        elif choice == "3":
            self._manual_actions_menu()
    
    def _view_action_details(self):
        """View detailed information about a specific action."""
        action_id = Prompt.ask("Enter action ID to view details", default="")
        
        if action_id.isdigit():
            actions = self.db.get_recent_actions(hours=168, limit=1000)  # Last week
            action = next((a for a in actions if a['id'] == int(action_id)), None)
            
            if action:
                self.console.print(Panel(f"""
[bold]Action Details[/bold]

ID: {action['id']}
Timestamp: {action['timestamp']}
Type: {action['action_type']}
Description: {action['description']}
Status: {action['status']}
Execution Time: {action['execution_time'] or 'N/A'}s
Error Message: {action['error_message'] or 'None'}
Trigger Alert ID: {action['trigger_alert_id'] or 'Manual'}
""", title="Action Information"))
            else:
                self.console.print(f"[red]Action {action_id} not found[/red]")
        else:
            self.console.print("[red]Invalid action ID[/red]")
        
        input("Press Enter to continue...")
    
    def _export_actions(self):
        """Export actions to CSV file."""
        filename = f"actions_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        
        if self.db.export_data('actions', filename, days=7):
            self.console.print(f"[green]Actions exported to {filename}[/green]")
        else:
            self.console.print("[red]Failed to export actions[/red]")
        
        input("Press Enter to continue...")
    
    def _manual_actions_menu(self):
        """Display manual actions menu."""
        self.console.clear()
        
        self.console.print(Panel("""
[bold red]Manual Actions Menu[/bold red]

[yellow]⚠️  Warning: These actions can affect system performance[/yellow]

Available Actions:
1. Restart System Service
2. Clear Temporary Files
3. Kill High CPU Process
4. Check Disk Usage
5. Force System Cleanup
6. Test Email Notifications
7. Generate System Report
8. Back to Actions View
""", title="Manual Actions"))
        
        choice = Prompt.ask("Select action", 
                          choices=["1", "2", "3", "4", "5", "6", "7", "8"], 
                          default="8")
        
        if choice == "1":
            self._restart_service_interactive()
        elif choice == "2":
            self._clear_temp_files()
        elif choice == "3":
            self._kill_process_interactive()
        elif choice == "4":
            self._check_disk_usage()
        elif choice == "5":
            self._force_cleanup()
        elif choice == "6":
            self._test_notifications()
        elif choice == "7":
            self._generate_report()
    
    def _restart_service_interactive(self):
        """Interactive service restart."""
        service_name = Prompt.ask("Enter service name to restart")
        
        if service_name:
            confirm = Confirm.ask(f"Are you sure you want to restart '{service_name}'?")
            if confirm and self.actions:
                # Record action attempt
                action_id = self.db.insert_action(
                    "SERVICE_RESTART", 
                    f"Manual restart of service: {service_name}",
                    "PENDING"
                )
                
                self.console.print(f"[yellow]Attempting to restart {service_name}...[/yellow]")
                # Here you would call your action engine
                # result = self.actions.restart_service(service_name)
                self.console.print(f"[green]Service restart command sent[/green]")
            else:
                self.console.print("[yellow]Service restart cancelled[/yellow]")
        
        input("Press Enter to continue...")
    
    def _clear_temp_files(self):
        """Clear temporary files."""
        confirm = Confirm.ask("Clear temporary files (/tmp and cache)?")
        if confirm:
            self.console.print("[yellow]Clearing temporary files...[/yellow]")
            # Here you would call your cleanup action
            self.console.print("[green]Temporary files cleared[/green]")
        
        input("Press Enter to continue...")
    
    def _kill_process_interactive(self):
        """Interactive process termination."""
        self.console.print("[red]⚠️  Process termination can cause system instability[/red]")
        
        if not Confirm.ask("Do you want to continue?"):
            return
        
        process_name = Prompt.ask("Enter process name or PID")
        if process_name:
            confirm = Confirm.ask(f"Kill process '{process_name}'?")
            if confirm:
                self.console.print(f"[yellow]Terminating process {process_name}...[/yellow]")
                # Here you would call your process termination action
                self.console.print("[green]Process termination command sent[/green]")
        
        input("Press Enter to continue...")
    
    def _check_disk_usage(self):
        """Display disk usage information."""
        self.console.print("[blue]Checking disk usage...[/blue]")
        
        # Get recent disk metrics
        disk_metrics = self.db.get_recent_metrics(metrics_type="disk_percent", hours=1, limit=1)
        
        if disk_metrics:
            current_usage = disk_metrics[0]['value']
            color = self._get_status_color(current_usage, {'warning': 80, 'critical': 95})
            
            self.console.print(Panel(f"""
[bold]Disk Usage Report[/bold]

Current Usage: [{color}]{current_usage:.1f}%[/{color}]
Status: {'CRITICAL' if current_usage >= 95 else 'WARNING' if current_usage >= 80 else 'NORMAL'}

Recommendations:
- Clear temporary files if usage > 80%
- Archive old logs if usage > 90%
- Contact administrator if usage > 95%
""", title="Disk Status"))
        else:
            self.console.print("[yellow]No recent disk usage data available[/yellow]")
        
        input("Press Enter to continue...")
    
    def _force_cleanup(self):
        """Force system cleanup."""
        self.console.print("[red]⚠️  Force cleanup will remove temporary files and logs[/red]")
        
        if Confirm.ask("Proceed with force cleanup?"):
            with Progress(SpinnerColumn(), TextColumn("[progress.description]{task.description}")) as progress:
                task = progress.add_task("Cleaning system...", total=None)
                
                # Simulate cleanup process
                time.sleep(2)
                progress.update(task, description="Clearing temp files...")
                time.sleep(1)
                progress.update(task, description="Clearing logs...")
                time.sleep(1)
                progress.update(task, description="Cleanup complete")
            
            self.console.print("[green]System cleanup completed[/green]")
        
        input("Press Enter to continue...")
    
    def _test_notifications(self):
        """Test email notification system."""
        self.console.print("[blue]Testing notification system...[/blue]")
        
        # Create test alert
        alert_id = self.db.insert_alert(
            "TEST", "INFO", 
            "Test notification from console interface",
            "system_test"
        )
        
        self.console.print(f"[green]Test alert created (ID: {alert_id})[/green]")
        self.console.print("[yellow]Check your email for test notification[/yellow]")
        
        input("Press Enter to continue...")
    
    def _generate_report(self):
        """Generate system report."""
        self.console.print("[blue]Generating system report...[/blue]")
        
        # Get system summary
        summary = self.db.get_system_summary()
        
        report_content = f"""
[bold]System Health Report[/bold]
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

[bold]Metrics Summary (24h):[/bold]
- Total Metrics Collected: {summary.get('metrics_24h', 0)}
- Active Alerts: {summary.get('active_alerts', 0)}
- Actions Taken: {summary.get('actions_24h', 0)}

[bold]Current System Status:[/bold]
"""
        
        for metric, data in summary.get('latest_metrics', {}).items():
            report_content += f"- {metric}: {data['value']:.1f}%\n"
        
        report_content += f"""
[bold]Database Info:[/bold]
- Size: {summary.get('database_size', 0) / 1024:.1f} KB
"""
        
        self.console.print(Panel(report_content, title="System Report"))
        
        # Option to save report
        if Confirm.ask("Save report to file?"):
            filename = f"system_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            try:
                with open(filename, 'w') as f:
                    # Strip rich formatting for file output
                    clean_content = report_content.replace('[bold]', '').replace('[/bold]', '')
                    f.write(clean_content)
                self.console.print(f"[green]Report saved to {filename}[/green]")
            except Exception as e:
                self.console.print(f"[red]Failed to save report: {e}[/red]")
        
        input("Press Enter to continue...")
    
    def _show_configuration_view(self):
        """Display configuration options."""
        self.console.clear()
        
        self.console.print(Panel("""
[bold]Configuration Menu[/bold]

Current Settings:
1. Refresh Rate: {refresh_rate}s
2. Alert Thresholds
3. Notification Settings
4. Database Maintenance
5. Export Settings
6. Back to Dashboard

Select option to modify:
""".format(refresh_rate=self.refresh_rate), title="Configuration"))
        
        choice = Prompt.ask("Select option", choices=["1", "2", "3", "4", "5", "6"], default="6")
        
        if choice == "1":
            self._configure_refresh_rate()
        elif choice == "2":
            self._configure_thresholds()
        elif choice == "3":
            self._configure_notifications()
        elif choice == "4":
            self._database_maintenance()
        elif choice == "5":
            self._export_settings()
    
    def _configure_refresh_rate(self):
        """Configure dashboard refresh rate."""
        current_rate = self.refresh_rate
        new_rate = Prompt.ask(f"Enter new refresh rate in seconds (current: {current_rate})", 
                             default=str(current_rate))
        
        try:
            self.refresh_rate = max(1, int(new_rate))
            self.console.print(f"[green]Refresh rate set to {self.refresh_rate}s[/green]")
        except ValueError:
            self.console.print("[red]Invalid refresh rate[/red]")
        
        input("Press Enter to continue...")
    
    def _configure_thresholds(self):
        """Configure alert thresholds."""
        self.console.print(Panel("""
[bold]Alert Thresholds Configuration[/bold]

Current Thresholds:
- CPU Warning: 70%, Critical: 85%
- Memory Warning: 80%, Critical: 90%
- Disk Warning: 80%, Critical: 95%

[yellow]Note: Threshold modification requires system restart[/yellow]
""", title="Thresholds"))
        
        input("Press Enter to continue...")
    
    def _configure_notifications(self):
        """Configure notification settings."""
        self.console.print(Panel("""
[bold]Notification Settings[/bold]

[yellow]Email notifications configuration[/yellow]
[yellow]This feature requires email settings in config file[/yellow]

Current Status: [red]Not Configured[/red]

To configure email notifications:
1. Edit config.py file
2. Set SMTP server details
3. Configure recipient addresses
4. Restart the application
""", title="Notifications"))
        
        input("Press Enter to continue...")
    
    def _database_maintenance(self):
        """Database maintenance options."""
        self.console.print(Panel("""
[bold]Database Maintenance[/bold]

Available Options:
1. Clean old data (30+ days)
2. Vacuum database
3. View database stats
4. Backup database
5. Back to configuration
""", title="Database Maintenance"))
        
        choice = Prompt.ask("Select option", choices=["1", "2", "3", "4", "5"], default="5")
        
        if choice == "1":
            if Confirm.ask("Remove data older than 30 days?"):
                result = self.db.cleanup_old_data(30)
                self.console.print(f"[green]Cleanup completed: {result}[/green]")
        elif choice == "2":
            self.console.print("[blue]Vacuuming database...[/blue]")
            # Database vacuum would be implemented here
            self.console.print("[green]Database vacuum completed[/green]")
        elif choice == "3":
            summary = self.db.get_system_summary()
            self.console.print(f"Database size: {summary.get('database_size', 0) / 1024:.1f} KB")
        elif choice == "4":
            self.console.print("[blue]Creating database backup...[/blue]")
            # Backup implementation would go here
            self.console.print("[green]Backup created[/green]")
        
        if choice != "5":
            input("Press Enter to continue...")
    
    def _export_settings(self):
        """Export and data management settings."""
        self.console.print(Panel("""
[bold]Export & Data Management[/bold]

Available Exports:
1. Export all metrics (CSV)
2. Export all alerts (CSV)
3. Export all actions (CSV)
4. Full system export (JSON)
5. Back to configuration
""", title="Export Settings"))
        
        choice = Prompt.ask("Select option", choices=["1", "2", "3", "4", "5"], default="5")
        
        if choice in ["1", "2", "3"]:
            table_map = {"1": "metrics", "2": "alerts", "3": "actions"}
            table_name = table_map[choice]
            filename = f"{table_name}_full_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            
            if self.db.export_data(table_name, filename, days=30):
                self.console.print(f"[green]{table_name.title()} exported to {filename}[/green]")
            else:
                self.console.print(f"[red]Failed to export {table_name}[/red]")
        elif choice == "4":
            self.console.print("[blue]Full system export not yet implemented[/blue]")
        
        if choice != "5":
            input("Press Enter to continue...")
    
    def _show_help(self):
        """Display help information."""
        help_text = """
[bold]System Monitor Help[/bold]

[bold cyan]Keyboard Shortcuts:[/bold cyan]
- Q: Quit application
- R: Refresh display
- A: View alerts
- C: Configuration menu
- H: Show this help

[bold cyan]Dashboard View:[/bold cyan]
- Real-time system metrics
- Color-coded status indicators
- Recent alerts summary
- Trend sparklines

[bold cyan]Alerts View:[/bold cyan]
- Detailed alert history
- Alert resolution
- Export capabilities

[bold cyan]Actions View:[/bold cyan]
- Recent automated actions
- Manual action triggers
- Action history and details

[bold cyan]Configuration:[/bold cyan]
- Adjust refresh rates
- Configure thresholds
- Database maintenance
- Export settings

[bold cyan]Status Colors:[/bold cyan]
- [green]Green: Normal operation[/green]
- [yellow]Yellow: Warning level[/yellow]
- [red]Red: Critical level[/red]
- [blue]Blue: Information[/blue]

[bold cyan]Tips:[/bold cyan]
- Use Ctrl+C to exit safely
- Check alerts regularly
- Monitor disk usage
- Export data for analysis
"""
        
        self.console.print(Panel(help_text, title="Help & Documentation"))
        input("Press Enter to continue...")
    
    def run_dashboard(self):
        """Run the interactive dashboard."""
        self.running = True
        layout = self._create_dashboard_layout()
        
        try:
            with Live(layout, console=self.console, refresh_per_second=1/self.refresh_rate) as live:
                while self.running:
                    try:
                        self._update_dashboard_content(layout)
                        
                        # Check for keyboard input (non-blocking)
                        if keyboard.is_pressed('q'):
                            break
                        elif keyboard.is_pressed('r'):
                            continue  # Refresh
                        elif keyboard.is_pressed('a'):
                            live.stop()
                            self._show_alerts_view()
                            live.start()
                        elif keyboard.is_pressed('c'):
                            live.stop()
                            self._show_configuration_view()
                            live.start()
                        elif keyboard.is_pressed('h'):
                            live.stop()
                            self._show_help()
                            live.start()
                        
                        time.sleep(self.refresh_rate)
                        
                    except KeyboardInterrupt:
                        break
                        
        except Exception as e:
            self.console.print(f"[red]Dashboard error: {e}[/red]")
        
        self.console.print("[yellow]Dashboard stopped[/yellow]")
    
    def run_interactive_mode(self):
        """Run interactive menu mode."""
        self.running = True
        
        while self.running:
            try:
                self.console.clear()
                
                # Main menu
                menu_items = [
                    "Dashboard View",
                    "System Alerts",
                    "Recent Actions", 
                    "Configuration",
                    "Generate Report",
                    "Help",
                    "Exit"
                ]
                
                self.console.print(Panel("""
[bold blue]Autonomous System Manager[/bold blue]

Welcome to the interactive console interface.
Select an option from the menu below:
""", title="Main Menu"))
                
                for i, item in enumerate(menu_items, 1):
                    marker = "►" if i == self.selected_menu_item + 1 else " "
                    self.console.print(f"{marker} {i}. {item}")
                
                choice = Prompt.ask("\nSelect option", 
                                  choices=[str(i) for i in range(1, len(menu_items) + 1)], 
                                  default="1")
                
                if choice == "1":
                    self.console.clear()
                    self.console.print("[yellow]Starting dashboard... Press 'q' to return to menu[/yellow]")
                    time.sleep(1)
                    self.run_dashboard()
                elif choice == "2":
                    self._show_alerts_view()
                elif choice == "3":
                    self._show_actions_view()
                elif choice == "4":
                    self._show_configuration_view()
                elif choice == "5":
                    self._generate_report()
                elif choice == "6":
                    self._show_help()
                elif choice == "7":
                    self.running = False
                    
            except KeyboardInterrupt:
                self.running = False
            except Exception as e:
                self.console.print(f"[red]Error: {e}[/red]")
                input("Press Enter to continue...")
        
        self.console.print("[green]Goodbye![/green]")
    
    def run_simple_status(self):
        """Run simple status display mode."""
        try:
            # Get current metrics
            metrics = self._get_system_metrics()
            summary = self.db.get_system_summary()
            
            # Create simple status table
            table = Table(title="System Status", show_header=True)
            table.add_column("Metric", style="cyan")
            table.add_column("Value", style="magenta")
            table.add_column("Status", style="green")
            
            # Add metrics
            thresholds = {
                'cpu_percent': {'warning': 70, 'critical': 85},
                'memory_percent': {'warning': 80, 'critical': 90},
                'disk_percent': {'warning': 80, 'critical': 95}
            }
            
            for metric_type, data in metrics.items():
                if metric_type in thresholds:
                    value = data['current']
                    color = self._get_status_color(value, thresholds[metric_type])
                    status = "CRITICAL" if value >= thresholds[metric_type]['critical'] else \
                            "WARNING" if value >= thresholds[metric_type]['warning'] else "NORMAL"
                    
                    table.add_row(
                        metric_type.replace('_', ' ').title(),
                        f"{value:.1f}%",
                        f"[{color}]{status}[/{color}]"
                    )
            
            # Add summary info
            table.add_row("Active Alerts", str(summary.get('active_alerts', 0)), 
                         "[red]ALERTS[/red]" if summary.get('active_alerts', 0) > 0 else "[green]OK[/green]")
            
            self.console.print(table)
            
        except Exception as e:
            self.console.print(f"[red]Error displaying status: {e}[/red]")