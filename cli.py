#!/usr/bin/env python3
"""
Command Line Interface for Autonomous System Manager
Provides interactive CLI commands and menu-driven interface
"""

import click
import sys
import os
from datetime import datetime, timedelta
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.text import Text
from rich.prompt import Prompt, Confirm
from rich.progress import Progress, SpinnerColumn, TextColumn
import json

# Import other modules (these would be your other project files)
try:
    from collector import SystemCollector
    from detector import AnomalyDetector
    from actions import ActionEngine
    from alerts import AlertSystem
    from database import DatabaseManager
    from reports import ReportGenerator
    from config import Config
except ImportError as e:
    print(f"Warning: Could not import module: {e}")
    # Create mock classes for demonstration
    class SystemCollector: pass
    class AnomalyDetector: pass
    class ActionEngine: pass
    class AlertSystem: pass
    class DatabaseManager: pass
    class ReportGenerator: pass
    class Config: pass

console = Console()

class CLIManager:
    """Main CLI Manager class"""
    
    def __init__(self):
        self.config = Config()
        self.db = DatabaseManager()
        self.collector = SystemCollector()
        self.detector = AnomalyDetector()
        self.actions = ActionEngine()
        self.alerts = AlertSystem()
        self.reports = ReportGenerator()
    
    def show_status(self):
        """Display current system status"""
        console.print("\n[bold blue]System Status[/bold blue]")
        
        # Get current metrics
        try:
            metrics = self.collector.get_current_metrics()
            
            table = Table(title="Current System Metrics")
            table.add_column("Metric", style="cyan")
            table.add_column("Value", style="green")
            table.add_column("Status", style="yellow")
            
            # CPU Status
            cpu_status = "Normal" if metrics.get('cpu', 0) < 80 else "Warning" if metrics.get('cpu', 0) < 95 else "Critical"
            cpu_color = "green" if cpu_status == "Normal" else "yellow" if cpu_status == "Warning" else "red"
            table.add_row("CPU Usage", f"{metrics.get('cpu', 0):.1f}%", f"[{cpu_color}]{cpu_status}[/{cpu_color}]")
            
            # Memory Status
            mem_status = "Normal" if metrics.get('memory', 0) < 85 else "Warning" if metrics.get('memory', 0) < 95 else "Critical"
            mem_color = "green" if mem_status == "Normal" else "yellow" if mem_status == "Warning" else "red"
            table.add_row("Memory Usage", f"{metrics.get('memory', 0):.1f}%", f"[{mem_color}]{mem_status}[/{mem_color}]")
            
            # Disk Status
            disk_status = "Normal" if metrics.get('disk', 0) < 90 else "Warning" if metrics.get('disk', 0) < 98 else "Critical"
            disk_color = "green" if disk_status == "Normal" else "yellow" if disk_status == "Warning" else "red"
            table.add_row("Disk Usage", f"{metrics.get('disk', 0):.1f}%", f"[{disk_color}]{disk_status}[/{disk_color}]")
            
            table.add_row("Processes", str(metrics.get('processes', 0)), "[green]Normal[/green]")
            
            console.print(table)
            
            # Show recent alerts
            recent_alerts = self.db.get_recent_alerts(limit=3)
            if recent_alerts:
                console.print("\n[bold yellow]Recent Alerts:[/bold yellow]")
                for alert in recent_alerts:
                    timestamp = alert.get('timestamp', 'Unknown')
                    message = alert.get('message', 'No message')
                    severity = alert.get('severity', 'info')
                    color = "red" if severity == "critical" else "yellow" if severity == "warning" else "blue"
                    console.print(f"• [{color}]{timestamp}[/{color}] - {message}")
            
        except Exception as e:
            console.print(f"[red]Error retrieving status: {e}[/red]")
    
    def show_alerts(self, last_n=10):
        """Display recent alerts"""
        console.print(f"\n[bold blue]Last {last_n} Alerts[/bold blue]")
        
        try:
            alerts = self.db.get_recent_alerts(limit=last_n)
            
            if not alerts:
                console.print("[yellow]No alerts found[/yellow]")
                return
            
            table = Table(title=f"Recent Alerts ({len(alerts)} found)")
            table.add_column("Timestamp", style="cyan")
            table.add_column("Severity", style="yellow")
            table.add_column("Message", style="white")
            table.add_column("Status", style="green")
            
            for alert in alerts:
                timestamp = alert.get('timestamp', 'Unknown')
                severity = alert.get('severity', 'info')
                message = alert.get('message', 'No message')
                status = alert.get('status', 'active')
                
                severity_color = "red" if severity == "critical" else "yellow" if severity == "warning" else "blue"
                status_color = "green" if status == "resolved" else "red"
                
                table.add_row(
                    timestamp,
                    f"[{severity_color}]{severity.upper()}[/{severity_color}]",
                    message,
                    f"[{status_color}]{status.upper()}[/{status_color}]"
                )
            
            console.print(table)
            
        except Exception as e:
            console.print(f"[red]Error retrieving alerts: {e}[/red]")
    
    def show_action_history(self, last_n=10):
        """Display action history"""
        console.print(f"\n[bold blue]Action History (Last {last_n})[/bold blue]")
        
        try:
            actions = self.db.get_action_history(limit=last_n)
            
            if not actions:
                console.print("[yellow]No actions found[/yellow]")
                return
            
            table = Table(title=f"Recent Actions ({len(actions)} found)")
            table.add_column("Timestamp", style="cyan")
            table.add_column("Action", style="yellow")
            table.add_column("Target", style="white")
            table.add_column("Result", style="green")
            table.add_column("Duration", style="blue")
            
            for action in actions:
                timestamp = action.get('timestamp', 'Unknown')
                action_type = action.get('action_type', 'Unknown')
                target = action.get('target', 'N/A')
                result = action.get('result', 'Unknown')
                duration = action.get('duration', 'N/A')
                
                result_color = "green" if result == "success" else "red"
                
                table.add_row(
                    timestamp,
                    action_type,
                    target,
                    f"[{result_color}]{result.upper()}[/{result_color}]",
                    str(duration)
                )
            
            console.print(table)
            
        except Exception as e:
            console.print(f"[red]Error retrieving action history: {e}[/red]")
    
    def restart_service(self, service_name):
        """Restart a system service"""
        console.print(f"\n[bold yellow]Restarting service: {service_name}[/bold yellow]")
        
        if not Confirm.ask(f"Are you sure you want to restart '{service_name}'?"):
            console.print("[yellow]Operation cancelled[/yellow]")
            return
        
        try:
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console,
            ) as progress:
                task = progress.add_task(f"Restarting {service_name}...", total=None)
                
                # Simulate service restart (replace with actual implementation)
                result = self.actions.restart_service(service_name)
                
                if result.get('success', False):
                    console.print(f"[green]✓ Service '{service_name}' restarted successfully[/green]")
                else:
                    console.print(f"[red]✗ Failed to restart '{service_name}': {result.get('error', 'Unknown error')}[/red]")
                
        except Exception as e:
            console.print(f"[red]Error restarting service: {e}[/red]")
    
    def clean_disk(self, path="/tmp"):
        """Clean disk space at specified path"""
        console.print(f"\n[bold yellow]Cleaning disk space: {path}[/bold yellow]")
        
        if not Confirm.ask(f"Are you sure you want to clean '{path}'? This will delete temporary files."):
            console.print("[yellow]Operation cancelled[/yellow]")
            return
        
        try:
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console,
            ) as progress:
                task = progress.add_task(f"Cleaning {path}...", total=None)
                
                # Simulate disk cleanup (replace with actual implementation)
                result = self.actions.clean_disk(path)
                
                if result.get('success', False):
                    freed_space = result.get('freed_space', 0)
                    console.print(f"[green]✓ Cleaned '{path}' successfully. Freed {freed_space} MB[/green]")
                else:
                    console.print(f"[red]✗ Failed to clean '{path}': {result.get('error', 'Unknown error')}[/red]")
                
        except Exception as e:
            console.print(f"[red]Error cleaning disk: {e}[/red]")
    
    def export_data(self, format_type="csv", days=7):
        """Export system data"""
        console.print(f"\n[bold blue]Exporting data (last {days} days) as {format_type.upper()}[/bold blue]")
        
        try:
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console,
            ) as progress:
                task = progress.add_task("Exporting data...", total=None)
                
                # Generate filename with timestamp
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"system_data_{timestamp}.{format_type}"
                
                # Export data using reports module
                result = self.reports.export_data(
                    format_type=format_type,
                    days=days,
                    filename=filename
                )
                
                if result.get('success', False):
                    console.print(f"[green]✓ Data exported to '{filename}'[/green]")
                    console.print(f"Records exported: {result.get('record_count', 0)}")
                else:
                    console.print(f"[red]✗ Failed to export data: {result.get('error', 'Unknown error')}[/red]")
                
        except Exception as e:
            console.print(f"[red]Error exporting data: {e}[/red]")
    
    def interactive_menu(self):
        """Show interactive menu"""
        while True:
            console.clear()
            
            panel = Panel.fit(
                "[bold blue]Autonomous System Manager[/bold blue]\n\n"
                "[1] Show System Status\n"
                "[2] View Recent Alerts\n"
                "[3] View Action History\n"
                "[4] Restart Service\n"
                "[5] Clean Disk Space\n"
                "[6] Export Data\n"
                "[7] Configuration\n"
                "[8] Generate Report\n"
                "[0] Exit\n",
                title="Main Menu",
                border_style="blue"
            )
            
            console.print(panel)
            
            choice = Prompt.ask("Select an option", choices=["0", "1", "2", "3", "4", "5", "6", "7", "8"])
            
            if choice == "0":
                console.print("[green]Goodbye![/green]")
                break
            elif choice == "1":
                self.show_status()
                Prompt.ask("Press Enter to continue")
            elif choice == "2":
                count = Prompt.ask("Number of alerts to show", default="10")
                try:
                    self.show_alerts(int(count))
                except ValueError:
                    console.print("[red]Invalid number[/red]")
                Prompt.ask("Press Enter to continue")
            elif choice == "3":
                count = Prompt.ask("Number of actions to show", default="10")
                try:
                    self.show_action_history(int(count))
                except ValueError:
                    console.print("[red]Invalid number[/red]")
                Prompt.ask("Press Enter to continue")
            elif choice == "4":
                service = Prompt.ask("Service name to restart")
                self.restart_service(service)
                Prompt.ask("Press Enter to continue")
            elif choice == "5":
                path = Prompt.ask("Path to clean", default="/tmp")
                self.clean_disk(path)
                Prompt.ask("Press Enter to continue")
            elif choice == "6":
                format_type = Prompt.ask("Export format", choices=["csv", "json"], default="csv")
                days = Prompt.ask("Days of data", default="7")
                try:
                    self.export_data(format_type, int(days))
                except ValueError:
                    console.print("[red]Invalid number of days[/red]")
                Prompt.ask("Press Enter to continue")
            elif choice == "7":
                self.configuration_menu()
            elif choice == "8":
                self.generate_report_menu()
    
    def configuration_menu(self):
        """Configuration submenu"""
        while True:
            console.clear()
            
            panel = Panel.fit(
                "[bold yellow]Configuration Settings[/bold yellow]\n\n"
                "[1] View Current Settings\n"
                "[2] Set CPU Threshold\n"
                "[3] Set Memory Threshold\n"
                "[4] Set Disk Threshold\n"
                "[5] Toggle Email Alerts\n"
                "[6] Toggle Auto Actions\n"
                "[0] Back to Main Menu\n",
                title="Configuration",
                border_style="yellow"
            )
            
            console.print(panel)
            
            choice = Prompt.ask("Select an option", choices=["0", "1", "2", "3", "4", "5", "6"])
            
            if choice == "0":
                break
            elif choice == "1":
                self.show_configuration()
            elif choice == "2":
                self.set_threshold("cpu")
            elif choice == "3":
                self.set_threshold("memory")
            elif choice == "4":
                self.set_threshold("disk")
            elif choice == "5":
                self.toggle_setting("email_alerts")
            elif choice == "6":
                self.toggle_setting("auto_actions")
            
            if choice != "0":
                Prompt.ask("Press Enter to continue")
    
    def show_configuration(self):
        """Display current configuration"""
        console.print("\n[bold yellow]Current Configuration[/bold yellow]")
        
        table = Table(title="Settings")
        table.add_column("Setting", style="cyan")
        table.add_column("Value", style="green")
        
        table.add_row("CPU Threshold", f"{getattr(self.config, 'CPU_THRESHOLD', 80)}%")
        table.add_row("Memory Threshold", f"{getattr(self.config, 'MEMORY_THRESHOLD', 85)}%")
        table.add_row("Disk Threshold", f"{getattr(self.config, 'DISK_THRESHOLD', 90)}%")
        table.add_row("Email Alerts", str(getattr(self.config, 'EMAIL_ALERTS', True)))
        table.add_row("Auto Actions", str(getattr(self.config, 'AUTO_ACTIONS', True)))
        table.add_row("Monitoring Interval", f"{getattr(self.config, 'MONITORING_INTERVAL', 60)}s")
        
        console.print(table)
    
    def set_threshold(self, metric_type):
        """Set threshold for a metric"""
        current_value = getattr(self.config, f"{metric_type.upper()}_THRESHOLD", 80)
        console.print(f"\nCurrent {metric_type} threshold: {current_value}%")
        
        new_value = Prompt.ask(f"Enter new {metric_type} threshold (0-100)", default=str(current_value))
        
        try:
            threshold = int(new_value)
            if 0 <= threshold <= 100:
                setattr(self.config, f"{metric_type.upper()}_THRESHOLD", threshold)
                console.print(f"[green]✓ {metric_type.capitalize()} threshold set to {threshold}%[/green]")
            else:
                console.print("[red]✗ Threshold must be between 0 and 100[/red]")
        except ValueError:
            console.print("[red]✗ Invalid threshold value[/red]")
    
    def toggle_setting(self, setting_name):
        """Toggle a boolean setting"""
        current_value = getattr(self.config, setting_name.upper(), True)
        new_value = not current_value
        setattr(self.config, setting_name.upper(), new_value)
        
        console.print(f"[green]✓ {setting_name.replace('_', ' ').title()} {'enabled' if new_value else 'disabled'}[/green]")
    
    def generate_report_menu(self):
        """Report generation submenu"""
        console.print("\n[bold green]Generate Report[/bold green]")
        
        report_type = Prompt.ask(
            "Report type",
            choices=["summary", "detailed", "performance"],
            default="summary"
        )
        
        days = Prompt.ask("Days of data", default="7")
        
        try:
            days_int = int(days)
            console.print(f"\nGenerating {report_type} report for last {days_int} days...")
            
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console,
            ) as progress:
                task = progress.add_task("Generating report...", total=None)
                
                result = self.reports.generate_report(report_type, days_int)
                
                if result.get('success', False):
                    console.print(f"[green]✓ Report generated: {result.get('filename', 'report.txt')}[/green]")
                else:
                    console.print(f"[red]✗ Failed to generate report: {result.get('error', 'Unknown error')}[/red]")
                    
        except ValueError:
            console.print("[red]✗ Invalid number of days[/red]")


# Click CLI Commands
@click.group()
@click.version_option(version="1.0.0")
def cli():
    """Autonomous System Manager CLI"""
    pass

@cli.command()
def status():
    """Show current system status"""
    manager = CLIManager()
    manager.show_status()

@cli.command()
@click.option('--last', default=10, help='Number of recent alerts to show')
def alerts(last):
    """Show recent alerts"""
    manager = CLIManager()
    manager.show_alerts(last)

@cli.command()
@click.option('--history', is_flag=True, help='Show action history')
@click.option('--last', default=10, help='Number of recent actions to show')
def actions(history, last):
    """Show action history"""
    if history:
        manager = CLIManager()
        manager.show_action_history(last)

@cli.command()
@click.option('--set', 'setting', help='Setting to modify (cpu_threshold, memory_threshold, disk_threshold)')
@click.argument('value', required=False)
def config(setting, value):
    """View or modify configuration"""
    manager = CLIManager()
    
    if setting and value:
        if setting.endswith('_threshold'):
            try:
                threshold = int(value)
                metric_type = setting.replace('_threshold', '')
                setattr(manager.config, setting.upper(), threshold)
                console.print(f"[green]✓ {metric_type.capitalize()} threshold set to {threshold}%[/green]")
            except ValueError:
                console.print("[red]✗ Invalid threshold value[/red]")
        else:
            console.print(f"[red]✗ Unknown setting: {setting}[/red]")
    else:
        manager.show_configuration()

@cli.command()
@click.argument('service_name')
def restart_service(service_name):
    """Restart a system service"""
    manager = CLIManager()
    manager.restart_service(service_name)

@cli.command()
@click.argument('path', default='/tmp')
def clean_disk(path):
    """Clean disk space at specified path"""
    manager = CLIManager()
    manager.clean_disk(path)

@cli.command()
@click.option('--format', 'format_type', default='csv', type=click.Choice(['csv', 'json']), help='Export format')
@click.option('--days', default=7, help='Days of data to export')
def export_data(format_type, days):
    """Export system data"""
    manager = CLIManager()
    manager.export_data(format_type, days)

@cli.command()
def interactive():
    """Launch interactive menu"""
    manager = CLIManager()
    manager.interactive_menu()

@cli.command()
@click.option('--type', 'report_type', default='summary', 
              type=click.Choice(['summary', 'detailed', 'performance']), 
              help='Type of report to generate')
@click.option('--days', default=7, help='Days of data to include')
def report(report_type, days):
    """Generate system report"""
    manager = CLIManager()
    console.print(f"Generating {report_type} report for last {days} days...")
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console,
    ) as progress:
        task = progress.add_task("Generating report...", total=None)
        
        result = manager.reports.generate_report(report_type, days)
        
        if result.get('success', False):
            console.print(f"[green]✓ Report generated: {result.get('filename', 'report.txt')}[/green]")
        else:
            console.print(f"[red]✗ Failed to generate report: {result.get('error', 'Unknown error')}[/red]")

if __name__ == "__main__":
    cli()