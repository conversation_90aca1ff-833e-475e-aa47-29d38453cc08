#!/usr/bin/env python3
"""
Autonomous System Manager - Main Application Entry Point

This is the central hub for the autonomous system monitoring and management tool.
Supports multiple modes: dashboard, interactive, report, daemon, and CLI commands.
"""

import argparse
import asyncio
import signal
import sys
import time
import threading
from pathlib import Path
from datetime import datetime, timedelta
import logging

# Third-party imports
try:
    from rich.console import Console
    from rich.live import Live
    from rich.layout import Layout
    from rich.panel import Panel
    from rich.progress import Progress, SpinnerColumn, TextColumn
    from rich.prompt import Confirm, Prompt
    from rich.table import Table
    from rich.text import Text
    import click
except ImportError as e:
    print(f"Missing required library: {e}")
    print("Please install required packages: pip install rich click")
    sys.exit(1)

# Local imports (these would be your other modules)
try:
    from config import Config
    from collector import SystemMetricsCollector
    from detector import AnomalyDetector
    from actions import ActionEngine
    from alerts import AlertSystem
    from console_ui import ConsoleDashboard, InteractiveMenu
    from reports import ReportGenerator
    from database import DatabaseManager
    from utils.formatters import format_bytes, format_percentage, format_uptime
    from utils.charts import create_ascii_chart
except ImportError as e:
    print(f"Warning: Could not import module: {e}")
    print("Some features may not be available until all modules are implemented.")

# Global variables
console = Console()
running = True
config = None

class AutonomousSystemManager:
    """Main application class that orchestrates all components"""
    
    def __init__(self, config_path=None):
        self.config = Config(config_path)
        self.console = Console()
        self.db = DatabaseManager(self.config.database_path)
        self.collector = SystemMetricsCollector(self.config)
        self.detector = AnomalyDetector(self.config)
        self.action_engine = ActionEngine(self.config)
        self.alert_system = AlertSystem(self.config)
        self.dashboard = ConsoleDashboard(self.config)
        self.report_generator = ReportGenerator(self.config, self.db)
        self.running = False
        
        # Setup logging
        self._setup_logging()
        
    def _setup_logging(self):
        """Configure logging for the application"""
        log_level = getattr(logging, self.config.log_level.upper(), logging.INFO)
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.config.log_file),
                logging.StreamHandler() if self.config.console_logging else logging.NullHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def start_monitoring(self):
        """Start the background monitoring process"""
        self.running = True
        self.logger.info("Starting autonomous system monitoring...")
        
        # Start metric collection in background thread
        collection_thread = threading.Thread(target=self._collection_loop, daemon=True)
        collection_thread.start()
        
        # Start anomaly detection in background thread
        detection_thread = threading.Thread(target=self._detection_loop, daemon=True)
        detection_thread.start()
        
        return collection_thread, detection_thread
        
    def stop_monitoring(self):
        """Stop the monitoring process"""
        self.running = False
        self.logger.info("Stopping autonomous system monitoring...")
        
    def _collection_loop(self):
        """Background loop for collecting system metrics"""
        while self.running:
            try:
                metrics = self.collector.collect_all_metrics()
                self.db.store_metrics(metrics)
                
                # Check for immediate alerts
                if self._check_immediate_alerts(metrics):
                    self.alert_system.send_alerts()
                    
            except Exception as e:
                self.logger.error(f"Error in collection loop: {e}")
                
            time.sleep(self.config.monitoring_interval)
            
    def _detection_loop(self):
        """Background loop for anomaly detection"""
        while self.running:
            try:
                # Get recent metrics for analysis
                recent_metrics = self.db.get_recent_metrics(minutes=60)
                
                if recent_metrics:
                    anomalies = self.detector.detect_anomalies(recent_metrics)
                    
                    for anomaly in anomalies:
                        self.logger.warning(f"Anomaly detected: {anomaly}")
                        
                        # Trigger automated response if enabled
                        if self.config.auto_actions_enabled:
                            action_taken = self.action_engine.handle_anomaly(anomaly)
                            if action_taken:
                                self.alert_system.add_alert(
                                    f"Automated action taken: {action_taken}",
                                    severity="info"
                                )
                                
            except Exception as e:
                self.logger.error(f"Error in detection loop: {e}")
                
            time.sleep(self.config.detection_interval)
            
    def _check_immediate_alerts(self, metrics):
        """Check if metrics trigger immediate alerts"""
        alerts_triggered = False
        
        # CPU threshold check
        if metrics.get('cpu_percent', 0) > self.config.cpu_threshold:
            self.alert_system.add_alert(
                f"High CPU usage: {metrics['cpu_percent']:.1f}%",
                severity="warning"
            )
            alerts_triggered = True
            
        # Memory threshold check
        if metrics.get('memory_percent', 0) > self.config.memory_threshold:
            self.alert_system.add_alert(
                f"High memory usage: {metrics['memory_percent']:.1f}%",
                severity="warning"
            )
            alerts_triggered = True
            
        # Disk threshold check
        if metrics.get('disk_percent', 0) > self.config.disk_threshold:
            self.alert_system.add_alert(
                f"High disk usage: {metrics['disk_percent']:.1f}%",
                severity="critical"
            )
            alerts_triggered = True
            
        return alerts_triggered

def signal_handler(signum, frame):
    """Handle shutdown signals gracefully"""
    global running
    console.print("\n[yellow]Shutting down gracefully...[/yellow]")
    running = False
    sys.exit(0)

def run_dashboard_mode(manager):
    """Run the real-time dashboard interface"""
    console.print("[green]Starting Dashboard Mode[/green]")
    console.print("Press [bold]Ctrl+C[/bold] to exit\n")
    
    # Start monitoring
    collection_thread, detection_thread = manager.start_monitoring()
    
    try:
        with Live(manager.dashboard.create_layout(), refresh_per_second=2, console=console) as live:
            while running:
                try:
                    # Update dashboard with latest metrics
                    latest_metrics = manager.db.get_latest_metrics()
                    recent_alerts = manager.alert_system.get_recent_alerts(limit=5)
                    
                    # Update the live display
                    live.update(manager.dashboard.create_layout(latest_metrics, recent_alerts))
                    
                    time.sleep(manager.config.console_refresh_rate)
                    
                except KeyboardInterrupt:
                    break
                    
    except Exception as e:
        console.print(f"[red]Dashboard error: {e}[/red]")
    finally:
        manager.stop_monitoring()

def run_interactive_mode(manager):
    """Run the interactive menu interface"""
    console.print("[green]Starting Interactive Mode[/green]")
    
    menu = InteractiveMenu(manager, console)
    
    while running:
        try:
            choice = menu.show_main_menu()
            
            if choice == 'quit':
                break
            elif choice == 'status':
                menu.show_system_status()
            elif choice == 'alerts':
                menu.show_alerts()
            elif choice == 'actions':
                menu.show_actions_menu()
            elif choice == 'config':
                menu.show_config_menu()
            elif choice == 'reports':
                menu.show_reports_menu()
            elif choice == 'monitor':
                # Start brief monitoring session
                manager.start_monitoring()
                console.print("Monitoring for 30 seconds...")
                time.sleep(30)
                manager.stop_monitoring()
                
        except KeyboardInterrupt:
            break
        except Exception as e:
            console.print(f"[red]Error: {e}[/red]")
            
    console.print("[yellow]Exiting interactive mode[/yellow]")

def run_report_mode(manager, days=7, output_format="console"):
    """Generate and display system reports"""
    console.print(f"[green]Generating {days}-day system report...[/green]")
    
    try:
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
        ) as progress:
            task = progress.add_task("Analyzing system data...", total=None)
            
            # Generate comprehensive report
            report_data = manager.report_generator.generate_comprehensive_report(days)
            
            progress.update(task, description="Formatting report...")
            
            if output_format == "console":
                _display_console_report(report_data)
            elif output_format == "csv":
                filename = manager.report_generator.export_csv_report(report_data)
                console.print(f"[green]Report exported to: {filename}[/green]")
            elif output_format == "json":
                filename = manager.report_generator.export_json_report(report_data)
                console.print(f"[green]Report exported to: {filename}[/green]")
                
    except Exception as e:
        console.print(f"[red]Report generation failed: {e}[/red]")

def _display_console_report(report_data):
    """Display report data in formatted console output"""
    # System Overview Table
    overview_table = Table(title="System Overview")
    overview_table.add_column("Metric", style="cyan")
    overview_table.add_column("Current", style="green")
    overview_table.add_column("Average", style="yellow")
    overview_table.add_column("Peak", style="red")
    
    for metric, data in report_data.get('overview', {}).items():
        overview_table.add_row(
            metric.replace('_', ' ').title(),
            str(data.get('current', 'N/A')),
            str(data.get('average', 'N/A')),
            str(data.get('peak', 'N/A'))
        )
        
    console.print(overview_table)
    console.print()
    
    # Alerts Summary
    if report_data.get('alerts'):
        alerts_table = Table(title="Recent Alerts Summary")
        alerts_table.add_column("Severity", style="cyan")
        alerts_table.add_column("Count", style="green")
        alerts_table.add_column("Last Occurrence", style="yellow")
        
        for severity, data in report_data['alerts'].items():
            alerts_table.add_row(
                severity.title(),
                str(data.get('count', 0)),
                data.get('last_occurrence', 'Never')
            )
            
        console.print(alerts_table)
        console.print()
    
    # Actions Summary
    if report_data.get('actions'):
        console.print(f"[bold]Automated Actions Taken:[/bold] {report_data['actions'].get('total', 0)}")
        console.print(f"[green]Successful:[/green] {report_data['actions'].get('successful', 0)}")
        console.print(f"[red]Failed:[/red] {report_data['actions'].get('failed', 0)}")
        console.print()

def run_daemon_mode(manager):
    """Run in background daemon mode"""
    console.print("[green]Starting Daemon Mode[/green]")
    console.print("Running in background... Press Ctrl+C to stop")
    
    # Start monitoring
    collection_thread, detection_thread = manager.start_monitoring()
    
    try:
        # Keep the main thread alive
        while running:
            time.sleep(10)  # Check every 10 seconds
            
            # Optionally print status updates
            if manager.config.daemon_status_updates:
                latest_metrics = manager.db.get_latest_metrics()
                if latest_metrics:
                    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    cpu = latest_metrics.get('cpu_percent', 0)
                    memory = latest_metrics.get('memory_percent', 0)
                    console.print(f"[dim]{timestamp} - CPU: {cpu:.1f}% Memory: {memory:.1f}%[/dim]")
                    
    except KeyboardInterrupt:
        console.print("\n[yellow]Daemon stopped[/yellow]")
    finally:
        manager.stop_monitoring()

# CLI Commands using Click
@click.group()
@click.option('--config', '-c', help='Configuration file path')
@click.option('--verbose', '-v', is_flag=True, help='Verbose output')
@click.pass_context
def cli(ctx, config, verbose):
    """Autonomous System Manager - Command Line Interface"""
    ctx.ensure_object(dict)
    ctx.obj['config_path'] = config
    ctx.obj['verbose'] = verbose
    
    # Set up global console for verbose output
    if verbose:
        console.print("[dim]Verbose mode enabled[/dim]")

@cli.command()
@click.pass_context
def status(ctx):
    """Show current system status"""
    manager = AutonomousSystemManager(ctx.obj.get('config_path'))
    
    # Get current metrics
    current_metrics = manager.collector.collect_all_metrics()
    
    # Create status display
    status_table = Table(title="Current System Status")
    status_table.add_column("Metric", style="cyan")
    status_table.add_column("Value", style="green")
    status_table.add_column("Status", style="yellow")
    
    # Add rows for each metric
    metrics_info = [
        ("CPU Usage", f"{current_metrics.get('cpu_percent', 0):.1f}%", 
         "Normal" if current_metrics.get('cpu_percent', 0) < manager.config.cpu_threshold else "High"),
        ("Memory Usage", f"{current_metrics.get('memory_percent', 0):.1f}%",
         "Normal" if current_metrics.get('memory_percent', 0) < manager.config.memory_threshold else "High"),
        ("Disk Usage", f"{current_metrics.get('disk_percent', 0):.1f}%",
         "Normal" if current_metrics.get('disk_percent', 0) < manager.config.disk_threshold else "High"),
        ("Load Average", f"{current_metrics.get('load_avg', [0])[0]:.2f}", "Normal"),
        ("Processes", str(current_metrics.get('process_count', 0)), "Normal")
    ]
    
    for metric, value, status in metrics_info:
        status_color = "green" if status == "Normal" else "red"
        status_table.add_row(metric, value, f"[{status_color}]{status}[/{status_color}]")
    
    console.print(status_table)

@cli.command()
@click.option('--last', '-l', default=10, help='Number of recent alerts to show')
@click.pass_context
def alerts(ctx, last):
    """Show recent alerts"""
    manager = AutonomousSystemManager(ctx.obj.get('config_path'))
    recent_alerts = manager.alert_system.get_recent_alerts(limit=last)
    
    if not recent_alerts:
        console.print("[yellow]No recent alerts found[/yellow]")
        return
    
    alerts_table = Table(title=f"Last {last} Alerts")
    alerts_table.add_column("Time", style="cyan")
    alerts_table.add_column("Severity", style="yellow")
    alerts_table.add_column("Message", style="white")
    
    for alert in recent_alerts:
        severity_color = {
            'info': 'blue',
            'warning': 'yellow',
            'critical': 'red'
        }.get(alert.get('severity', 'info'), 'white')
        
        alerts_table.add_row(
            alert.get('timestamp', ''),
            f"[{severity_color}]{alert.get('severity', '').upper()}[/{severity_color}]",
            alert.get('message', '')
        )
    
    console.print(alerts_table)

@cli.command()
@click.option('--days', '-d', default=7, help='Number of days for report')
@click.option('--format', '-f', type=click.Choice(['console', 'csv', 'json']), 
              default='console', help='Output format')
@click.pass_context
def report(ctx, days, format):
    """Generate system report"""
    manager = AutonomousSystemManager(ctx.obj.get('config_path'))
    run_report_mode(manager, days, format)

@cli.command()
@click.option('--mode', '-m', type=click.Choice(['dashboard', 'interactive', 'daemon']),
              default='dashboard', help='Interface mode')
@click.pass_context
def monitor(ctx, mode):
    """Start system monitoring"""
    manager = AutonomousSystemManager(ctx.obj.get('config_path'))
    
    # Set up signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    if mode == 'dashboard':
        run_dashboard_mode(manager)
    elif mode == 'interactive':
        run_interactive_mode(manager)
    elif mode == 'daemon':
        run_daemon_mode(manager)

def main():
    """Main entry point - handles legacy argument parsing"""
    parser = argparse.ArgumentParser(description='Autonomous System Manager')
    parser.add_argument('--mode', choices=['dashboard', 'interactive', 'report', 'daemon'],
                        default='dashboard', help='Operating mode')
    parser.add_argument('--days', type=int, default=7, help='Days for report mode')
    parser.add_argument('--format', choices=['console', 'csv', 'json'], 
                        default='console', help='Report output format')
    parser.add_argument('--config', help='Configuration file path')
    
    args = parser.parse_args()
    
    # Initialize manager
    try:
        manager = AutonomousSystemManager(args.config)
    except Exception as e:
        console.print(f"[red]Failed to initialize system manager: {e}[/red]")
        sys.exit(1)
    
    # Set up signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Run appropriate mode
    try:
        if args.mode == 'dashboard':
            run_dashboard_mode(manager)
        elif args.mode == 'interactive':
            run_interactive_mode(manager)
        elif args.mode == 'report':
            run_report_mode(manager, args.days, args.format)
        elif args.mode == 'daemon':
            run_daemon_mode(manager)
    except Exception as e:
        console.print(f"[red]Application error: {e}[/red]")
        sys.exit(1)

if __name__ == "__main__":
    # Check if being run with click commands
    if len(sys.argv) > 1 and sys.argv[1] in ['status', 'alerts', 'report', 'monitor']:
        cli()
    else:
        main()