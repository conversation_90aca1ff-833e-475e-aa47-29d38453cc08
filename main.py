#!/usr/bin/env python3
"""
Autonomous System Manager - Main Application Entry Point

This is the central hub for the autonomous system monitoring and management tool.
Supports multiple modes: dashboard, interactive, report, daemon, and CLI commands.
"""

import argparse
import asyncio
import signal
import sys
import time
import threading
from pathlib import Path
from datetime import datetime, timedelta
import logging

# Third-party imports
try:
    from rich.console import Console
    from rich.live import Live
    from rich.layout import Layout
    from rich.panel import Panel
    from rich.progress import Progress, SpinnerColumn, TextColumn
    from rich.prompt import Confirm, Prompt
    from rich.table import Table
    from rich.text import Text
    import click
except ImportError as e:
    print(f"Missing required library: {e}")
    print("Please install required packages: pip install rich click")
    sys.exit(1)

# Local imports (these would be your other modules)
try:
    from config import Config
    from collector import SystemCollector
    from detector import AnomalyDetector
    from actions import ActionEngine
    from alerts import AlertManager
    from console_ui import ConsoleUI
    from reports import ReportGenerator
    from database import DatabaseManager
    from utils.formatters import DataFormatter
    from utils.charts import ASCIIChart
except ImportError as e:
    print(f"Warning: Could not import module: {e}")
    print("Some features may not be available until all modules are implemented.")

    # Create mock classes for missing modules
    class Config:
        def __init__(self):
            self.DATABASE_PATH = 'system_monitor.db'
            self.LOG_LEVEL = 'INFO'
            self.LOG_FILE = 'system_monitor.log'
            self.MONITORING_INTERVAL = 60
            self.CPU_THRESHOLD = 80
            self.MEMORY_THRESHOLD = 85
            self.DISK_THRESHOLD = 90
            self.AUTO_ACTIONS_ENABLED = True
            self.REQUIRE_APPROVAL = False
            self.MAX_ALERTS_PER_HOUR = 10
            self.ENABLE_SYSTEM_REBOOT = False
            self.ENABLE_PROCESS_KILL = False
            self.CRITICAL_SERVICES = []
            self.PROTECTED_PROCESSES = []

        def get_alert_config(self):
            return {
                'EMAIL_ALERTS': False,
                'ALERT_COOLDOWN_MINUTES': 5,
                'MAX_ALERTS_PER_HOUR': 10
            }

    class SystemCollector:
        def collect_all_metrics(self):
            return {
                'cpu': {'usage_percent': 0},
                'memory': {'usage_percent': 0},
                'disk': {'partitions': {}},
                'processes': {'total_count': 0}
            }

    class AnomalyDetector:
        def detect_all_anomalies(self, current_metrics, historical_data):
            return {'total_count': 0, 'threshold_violations': []}

    class ActionEngine:
        def __init__(self, config=None):
            pass

    class AlertManager:
        def __init__(self, config=None):
            pass
        def create_system_alert(self, *args):
            pass
        def get_recent_alerts(self, **kwargs):
            return []

    class ConsoleUI:
        def __init__(self, *args):
            pass

    class ReportGenerator:
        def generate_comprehensive_report(self, days):
            return {'overview': {}, 'alerts': {}, 'actions': {}}
        def export_csv_report(self, data):
            return 'report.csv'
        def export_json_report(self, data):
            return 'report.json'

    class DatabaseManager:
        def __init__(self, path=None):
            pass
        def get_recent_alerts(self, **kwargs):
            return []
        def get_recent_metrics(self, **kwargs):
            return []
        def get_latest_metrics(self):
            return {}
        def insert_metric(self, *args):
            pass

    class DataFormatter:
        pass

    class ASCIIChart:
        pass

# Global variables
console = Console()
running = True
config = None

class AutonomousSystemManager:
    """Main application class that orchestrates all components"""
    
    def __init__(self, config_path=None):
        self.config = Config()
        self.console = Console()
        self.db = DatabaseManager(getattr(self.config, 'DATABASE_PATH', 'system_monitor.db'))
        self.collector = SystemCollector()
        self.detector = AnomalyDetector()
        self.action_engine = ActionEngine({
            'AUTO_ACTIONS': getattr(self.config, 'AUTO_ACTIONS_ENABLED', True),
            'REQUIRE_APPROVAL': getattr(self.config, 'REQUIRE_APPROVAL', False),
            'MAX_ACTIONS_PER_HOUR': getattr(self.config, 'MAX_ALERTS_PER_HOUR', 10),
            'ALLOW_SYSTEM_RESTART': getattr(self.config, 'ENABLE_SYSTEM_REBOOT', False),
            'ALLOW_ROOT_KILL': getattr(self.config, 'ENABLE_PROCESS_KILL', False),
            'CRITICAL_SERVICES': getattr(self.config, 'CRITICAL_SERVICES', []),
            'PROCESS_WHITELIST': getattr(self.config, 'PROTECTED_PROCESSES', [])
        })
        self.alert_system = AlertManager(self.config.get_alert_config())
        self.dashboard = ConsoleUI(self.db, self.collector, self.action_engine)
        self.report_generator = ReportGenerator()
        self.running = False
        
        # Setup logging
        self._setup_logging()
        
    def _setup_logging(self):
        """Configure logging for the application"""
        log_level = getattr(logging, getattr(self.config, 'LOG_LEVEL', 'INFO').upper(), logging.INFO)
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(getattr(self.config, 'LOG_FILE', 'system_monitor.log')),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def start_monitoring(self):
        """Start the background monitoring process"""
        self.running = True
        self.logger.info("Starting autonomous system monitoring...")
        
        # Start metric collection in background thread
        collection_thread = threading.Thread(target=self._collection_loop, daemon=True)
        collection_thread.start()
        
        # Start anomaly detection in background thread
        detection_thread = threading.Thread(target=self._detection_loop, daemon=True)
        detection_thread.start()
        
        return collection_thread, detection_thread
        
    def stop_monitoring(self):
        """Stop the monitoring process"""
        self.running = False
        self.logger.info("Stopping autonomous system monitoring...")
        
    def _collection_loop(self):
        """Background loop for collecting system metrics"""
        while self.running:
            try:
                metrics = self.collector.collect_all_metrics()

                # Store metrics in database
                for metric_type, data in metrics.items():
                    if isinstance(data, dict) and 'usage_percent' in data:
                        self.db.insert_metric(metric_type, data['usage_percent'], '%')
                    elif isinstance(data, (int, float)):
                        self.db.insert_metric(metric_type, data)

                # Check for immediate alerts
                if self._check_immediate_alerts(metrics):
                    pass  # Alert system will handle notifications

            except Exception as e:
                self.logger.error(f"Error in collection loop: {e}")

            time.sleep(getattr(self.config, 'MONITORING_INTERVAL', 60))
            
    def _detection_loop(self):
        """Background loop for anomaly detection"""
        while self.running:
            try:
                # Get recent metrics for analysis
                recent_metrics = self.db.get_recent_metrics(hours=1)

                if recent_metrics:
                    # Convert to format expected by detector
                    metrics_data = []
                    for metric in recent_metrics:
                        metrics_data.append({
                            'timestamp': metric['timestamp'],
                            metric['metric_type']: metric['value']
                        })

                    if metrics_data:
                        # Get current metrics for anomaly detection
                        current_metrics = self.collector.collect_all_metrics()
                        anomalies = self.detector.detect_all_anomalies(current_metrics, metrics_data)

                        # Process any detected anomalies
                        if anomalies.get('total_count', 0) > 0:
                            self.logger.warning(f"Anomalies detected: {anomalies['total_count']}")

                            # Create alerts for threshold violations
                            for violation in anomalies.get('threshold_violations', []):
                                self.alert_system.create_system_alert(
                                    violation['metric'],
                                    violation['current_value'],
                                    violation['threshold'],
                                    violation.get('severity', 'MEDIUM')
                                )

            except Exception as e:
                self.logger.error(f"Error in detection loop: {e}")

            time.sleep(getattr(self.config, 'MONITORING_INTERVAL', 60) * 2)  # Run detection less frequently
            
    def _check_immediate_alerts(self, metrics):
        """Check if metrics trigger immediate alerts"""
        alerts_triggered = False

        # CPU threshold check
        cpu_usage = metrics.get('cpu', {}).get('usage_percent', 0)
        if cpu_usage > getattr(self.config, 'CPU_THRESHOLD', 80):
            self.alert_system.create_system_alert(
                'cpu_usage', cpu_usage, getattr(self.config, 'CPU_THRESHOLD', 80)
            )
            alerts_triggered = True

        # Memory threshold check
        memory_usage = metrics.get('memory', {}).get('usage_percent', 0)
        if memory_usage > getattr(self.config, 'MEMORY_THRESHOLD', 85):
            self.alert_system.create_system_alert(
                'memory_usage', memory_usage, getattr(self.config, 'MEMORY_THRESHOLD', 85)
            )
            alerts_triggered = True

        # Disk threshold check - check all partitions
        disk_partitions = metrics.get('disk', {}).get('partitions', {})
        for partition, info in disk_partitions.items():
            disk_usage = info.get('usage_percent', 0)
            if disk_usage > getattr(self.config, 'DISK_THRESHOLD', 90):
                self.alert_system.create_system_alert(
                    'disk_usage', disk_usage, getattr(self.config, 'DISK_THRESHOLD', 90)
                )
                alerts_triggered = True

        return alerts_triggered

def signal_handler(signum, frame):
    """Handle shutdown signals gracefully"""
    global running
    console.print("\n[yellow]Shutting down gracefully...[/yellow]")
    running = False
    sys.exit(0)

def run_dashboard_mode(manager):
    """Run the real-time dashboard interface"""
    console.print("[green]Starting Dashboard Mode[/green]")
    console.print("Press [bold]Ctrl+C[/bold] to exit\n")
    
    # Start monitoring
    collection_thread, detection_thread = manager.start_monitoring()
    
    try:
        # Simple dashboard display
        while running:
            try:
                console.clear()
                console.print("[bold blue]System Dashboard[/bold blue]")

                # Get current metrics
                metrics = manager.collector.collect_all_metrics()

                # Display metrics
                console.print(f"CPU: {metrics.get('cpu', {}).get('usage_percent', 0):.1f}%")
                console.print(f"Memory: {metrics.get('memory', {}).get('usage_percent', 0):.1f}%")
                console.print(f"Processes: {metrics.get('processes', {}).get('total_count', 0)}")

                # Get recent alerts
                recent_alerts = manager.db.get_recent_alerts(hours=1, limit=5)
                console.print(f"\nRecent Alerts: {len(recent_alerts)}")
                for alert in recent_alerts[:3]:
                    console.print(f"  - {alert.get('severity', 'INFO')}: {alert.get('message', 'No message')}")

                console.print("\nPress Ctrl+C to exit")
                time.sleep(getattr(manager.config, 'MONITORING_INTERVAL', 5))

            except KeyboardInterrupt:
                break
                    
    except Exception as e:
        console.print(f"[red]Dashboard error: {e}[/red]")
    finally:
        manager.stop_monitoring()

def run_interactive_mode(manager):
    """Run the interactive menu interface"""
    console.print("[green]Starting Interactive Mode[/green]")

    # Use the dashboard's interactive features
    menu = manager.dashboard
    
    while running:
        try:
            console.print("\n[bold blue]Interactive Menu[/bold blue]")
            console.print("1. Show System Status")
            console.print("2. View Alerts")
            console.print("3. View Actions")
            console.print("4. Generate Report")
            console.print("5. Start Monitoring")
            console.print("0. Exit")

            choice = Prompt.ask("Select option", choices=["0", "1", "2", "3", "4", "5"], default="0")

            if choice == "0":
                break
            elif choice == "1":
                # Show current metrics
                metrics = manager.collector.collect_all_metrics()
                console.print(f"CPU: {metrics.get('cpu', {}).get('usage_percent', 0):.1f}%")
                console.print(f"Memory: {metrics.get('memory', {}).get('usage_percent', 0):.1f}%")
                console.print(f"Disk: {metrics.get('disk', {}).get('partitions', {})}")
            elif choice == "2":
                menu._show_alerts_view()
            elif choice == "3":
                menu._show_actions_view()
            elif choice == "4":
                report = manager.report_generator.generate_report("summary", 7)
                console.print(report.get('content', 'Report generation failed'))
            elif choice == "5":
                # Start brief monitoring session
                manager.start_monitoring()
                console.print("Monitoring for 30 seconds...")
                time.sleep(30)
                manager.stop_monitoring()

        except KeyboardInterrupt:
            break
        except Exception as e:
            console.print(f"[red]Error: {e}[/red]")
            
    console.print("[yellow]Exiting interactive mode[/yellow]")

def run_report_mode(manager, days=7, output_format="console"):
    """Generate and display system reports"""
    console.print(f"[green]Generating {days}-day system report...[/green]")
    
    try:
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
        ) as progress:
            task = progress.add_task("Analyzing system data...", total=None)
            
            # Generate comprehensive report
            report_data = manager.report_generator.generate_comprehensive_report(days)
            
            progress.update(task, description="Formatting report...")
            
            if output_format == "console":
                _display_console_report(report_data)
            elif output_format == "csv":
                filename = manager.report_generator.export_csv_report(report_data)
                console.print(f"[green]Report exported to: {filename}[/green]")
            elif output_format == "json":
                filename = manager.report_generator.export_json_report(report_data)
                console.print(f"[green]Report exported to: {filename}[/green]")
                
    except Exception as e:
        console.print(f"[red]Report generation failed: {e}[/red]")

def _display_console_report(report_data):
    """Display report data in formatted console output"""
    # System Overview Table
    overview_table = Table(title="System Overview")
    overview_table.add_column("Metric", style="cyan")
    overview_table.add_column("Current", style="green")
    overview_table.add_column("Average", style="yellow")
    overview_table.add_column("Peak", style="red")
    
    for metric, data in report_data.get('overview', {}).items():
        overview_table.add_row(
            metric.replace('_', ' ').title(),
            str(data.get('current', 'N/A')),
            str(data.get('average', 'N/A')),
            str(data.get('peak', 'N/A'))
        )
        
    console.print(overview_table)
    console.print()
    
    # Alerts Summary
    if report_data.get('alerts'):
        alerts_table = Table(title="Recent Alerts Summary")
        alerts_table.add_column("Severity", style="cyan")
        alerts_table.add_column("Count", style="green")
        alerts_table.add_column("Last Occurrence", style="yellow")
        
        for severity, data in report_data['alerts'].items():
            alerts_table.add_row(
                severity.title(),
                str(data.get('count', 0)),
                data.get('last_occurrence', 'Never')
            )
            
        console.print(alerts_table)
        console.print()
    
    # Actions Summary
    if report_data.get('actions'):
        console.print(f"[bold]Automated Actions Taken:[/bold] {report_data['actions'].get('total', 0)}")
        console.print(f"[green]Successful:[/green] {report_data['actions'].get('successful', 0)}")
        console.print(f"[red]Failed:[/red] {report_data['actions'].get('failed', 0)}")
        console.print()

def run_daemon_mode(manager):
    """Run in background daemon mode"""
    console.print("[green]Starting Daemon Mode[/green]")
    console.print("Running in background... Press Ctrl+C to stop")
    
    # Start monitoring
    collection_thread, detection_thread = manager.start_monitoring()
    
    try:
        # Keep the main thread alive
        while running:
            time.sleep(10)  # Check every 10 seconds
            
            # Optionally print status updates
            if manager.config.daemon_status_updates:
                latest_metrics = manager.db.get_latest_metrics()
                if latest_metrics:
                    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    cpu = latest_metrics.get('cpu_percent', 0)
                    memory = latest_metrics.get('memory_percent', 0)
                    console.print(f"[dim]{timestamp} - CPU: {cpu:.1f}% Memory: {memory:.1f}%[/dim]")
                    
    except KeyboardInterrupt:
        console.print("\n[yellow]Daemon stopped[/yellow]")
    finally:
        manager.stop_monitoring()

# CLI Commands using Click
@click.group()
@click.option('--config', '-c', help='Configuration file path')
@click.option('--verbose', '-v', is_flag=True, help='Verbose output')
@click.pass_context
def cli(ctx, config, verbose):
    """Autonomous System Manager - Command Line Interface"""
    ctx.ensure_object(dict)
    ctx.obj['config_path'] = config
    ctx.obj['verbose'] = verbose
    
    # Set up global console for verbose output
    if verbose:
        console.print("[dim]Verbose mode enabled[/dim]")

@cli.command()
@click.pass_context
def status(ctx):
    """Show current system status"""
    manager = AutonomousSystemManager(ctx.obj.get('config_path'))
    
    # Get current metrics
    current_metrics = manager.collector.collect_all_metrics()
    
    # Create status display
    status_table = Table(title="Current System Status")
    status_table.add_column("Metric", style="cyan")
    status_table.add_column("Value", style="green")
    status_table.add_column("Status", style="yellow")
    
    # Add rows for each metric
    metrics_info = [
        ("CPU Usage", f"{current_metrics.get('cpu_percent', 0):.1f}%", 
         "Normal" if current_metrics.get('cpu_percent', 0) < manager.config.cpu_threshold else "High"),
        ("Memory Usage", f"{current_metrics.get('memory_percent', 0):.1f}%",
         "Normal" if current_metrics.get('memory_percent', 0) < manager.config.memory_threshold else "High"),
        ("Disk Usage", f"{current_metrics.get('disk_percent', 0):.1f}%",
         "Normal" if current_metrics.get('disk_percent', 0) < manager.config.disk_threshold else "High"),
        ("Load Average", f"{current_metrics.get('load_avg', [0])[0]:.2f}", "Normal"),
        ("Processes", str(current_metrics.get('process_count', 0)), "Normal")
    ]
    
    for metric, value, status in metrics_info:
        status_color = "green" if status == "Normal" else "red"
        status_table.add_row(metric, value, f"[{status_color}]{status}[/{status_color}]")
    
    console.print(status_table)

@cli.command()
@click.option('--last', '-l', default=10, help='Number of recent alerts to show')
@click.pass_context
def alerts(ctx, last):
    """Show recent alerts"""
    manager = AutonomousSystemManager(ctx.obj.get('config_path'))
    recent_alerts = manager.alert_system.get_recent_alerts(limit=last)
    
    if not recent_alerts:
        console.print("[yellow]No recent alerts found[/yellow]")
        return
    
    alerts_table = Table(title=f"Last {last} Alerts")
    alerts_table.add_column("Time", style="cyan")
    alerts_table.add_column("Severity", style="yellow")
    alerts_table.add_column("Message", style="white")
    
    for alert in recent_alerts:
        severity_color = {
            'info': 'blue',
            'warning': 'yellow',
            'critical': 'red'
        }.get(alert.get('severity', 'info'), 'white')
        
        alerts_table.add_row(
            alert.get('timestamp', ''),
            f"[{severity_color}]{alert.get('severity', '').upper()}[/{severity_color}]",
            alert.get('message', '')
        )
    
    console.print(alerts_table)

@cli.command()
@click.option('--days', '-d', default=7, help='Number of days for report')
@click.option('--format', '-f', type=click.Choice(['console', 'csv', 'json']), 
              default='console', help='Output format')
@click.pass_context
def report(ctx, days, format):
    """Generate system report"""
    manager = AutonomousSystemManager(ctx.obj.get('config_path'))
    run_report_mode(manager, days, format)

@cli.command()
@click.option('--mode', '-m', type=click.Choice(['dashboard', 'interactive', 'daemon']),
              default='dashboard', help='Interface mode')
@click.pass_context
def monitor(ctx, mode):
    """Start system monitoring"""
    manager = AutonomousSystemManager(ctx.obj.get('config_path'))
    
    # Set up signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    if mode == 'dashboard':
        run_dashboard_mode(manager)
    elif mode == 'interactive':
        run_interactive_mode(manager)
    elif mode == 'daemon':
        run_daemon_mode(manager)

def main():
    """Main entry point - handles legacy argument parsing"""
    parser = argparse.ArgumentParser(description='Autonomous System Manager')
    parser.add_argument('--mode', choices=['dashboard', 'interactive', 'report', 'daemon'],
                        default='dashboard', help='Operating mode')
    parser.add_argument('--days', type=int, default=7, help='Days for report mode')
    parser.add_argument('--format', choices=['console', 'csv', 'json'], 
                        default='console', help='Report output format')
    parser.add_argument('--config', help='Configuration file path')
    
    args = parser.parse_args()
    
    # Initialize manager
    try:
        manager = AutonomousSystemManager(args.config)
    except Exception as e:
        console.print(f"[red]Failed to initialize system manager: {e}[/red]")
        sys.exit(1)
    
    # Set up signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Run appropriate mode
    try:
        if args.mode == 'dashboard':
            run_dashboard_mode(manager)
        elif args.mode == 'interactive':
            run_interactive_mode(manager)
        elif args.mode == 'report':
            run_report_mode(manager, args.days, args.format)
        elif args.mode == 'daemon':
            run_daemon_mode(manager)
    except Exception as e:
        console.print(f"[red]Application error: {e}[/red]")
        sys.exit(1)

if __name__ == "__main__":
    # Check if being run with click commands
    if len(sys.argv) > 1 and sys.argv[1] in ['status', 'alerts', 'report', 'monitor']:
        cli()
    else:
        main()