import os
import json
import csv
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import pandas as pd
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.text import Text
import plotext as plt
from tabulate import tabulate

# Import other modules (these would be your other project files)
try:
    from database import DatabaseManager
    from config import Config
except ImportError as e:
    print(f"Warning: Could not import module: {e}")
    # Create mock classes for demonstration
    class DatabaseManager:
        def get_metrics_data(self, days=7): return []
        def get_recent_alerts(self, limit=10): return []
        def get_action_history(self, limit=10): return []
    class Config: pass

console = Console()

class ReportGenerator:
    """Main report generation class"""
    
    def __init__(self):
        self.config = Config()
        self.db = DatabaseManager()
        self.reports_dir = "reports"
        self.ensure_reports_directory()
    
    def ensure_reports_directory(self):
        """Create reports directory if it doesn't exist"""
        if not os.path.exists(self.reports_dir):
            os.makedirs(self.reports_dir)
    
    def generate_report(self, report_type: str = "summary", days: int = 7) -> Dict[str, Any]:
        """Generate different types of reports"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{self.reports_dir}/{report_type}_report_{timestamp}.txt"
            
            if report_type == "summary":
                content = self._generate_summary_report(days)
            elif report_type == "detailed":
                content = self._generate_detailed_report(days)
            elif report_type == "performance":
                content = self._generate_performance_report(days)
            else:
                return {"success": False, "error": f"Unknown report type: {report_type}"}
            
            # Write report to file
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(content)
            
            # Also display in console
            console.print(Panel(content, title=f"{report_type.title()} Report", border_style="green"))
            
            return {
                "success": True,
                "filename": filename,
                "content": content
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _generate_summary_report(self, days: int) -> str:
        """Generate a summary report"""
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        report_lines = []
        report_lines.append("=" * 60)
        report_lines.append("AUTONOMOUS SYSTEM MANAGER - SUMMARY REPORT")
        report_lines.append("=" * 60)
        report_lines.append(f"Report Period: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
        report_lines.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append("")
        
        # System Overview
        report_lines.append("SYSTEM OVERVIEW")
        report_lines.append("-" * 20)
        
        try:
            # Get metrics data
            metrics_data = self.db.get_metrics_data(days=days)
            
            if metrics_data:
                # Calculate averages
                cpu_avg = sum(m.get('cpu', 0) for m in metrics_data) / len(metrics_data)
                memory_avg = sum(m.get('memory', 0) for m in metrics_data) / len(metrics_data)
                disk_avg = sum(m.get('disk', 0) for m in metrics_data) / len(metrics_data)
                
                # Calculate peaks
                cpu_max = max(m.get('cpu', 0) for m in metrics_data)
                memory_max = max(m.get('memory', 0) for m in metrics_data)
                disk_max = max(m.get('disk', 0) for m in metrics_data)
                
                report_lines.append(f"Average CPU Usage:    {cpu_avg:.1f}% (Peak: {cpu_max:.1f}%)")
                report_lines.append(f"Average Memory Usage: {memory_avg:.1f}% (Peak: {memory_max:.1f}%)")
                report_lines.append(f"Average Disk Usage:   {disk_avg:.1f}% (Peak: {disk_max:.1f}%)")
                report_lines.append(f"Total Data Points:    {len(metrics_data)}")
            else:
                report_lines.append("No metrics data available for the specified period.")
            
        except Exception as e:
            report_lines.append(f"Error retrieving metrics: {e}")
        
        report_lines.append("")
        
        # Alerts Summary
        report_lines.append("ALERTS SUMMARY")
        report_lines.append("-" * 15)
        
        try:
            alerts = self.db.get_recent_alerts(limit=100)  # Get more alerts for analysis
            
            if alerts:
                # Count by severity
                severity_counts = {"critical": 0, "warning": 0, "info": 0}
                for alert in alerts:
                    severity = alert.get('severity', 'info')
                    if severity in severity_counts:
                        severity_counts[severity] += 1
                
                report_lines.append(f"Total Alerts:         {len(alerts)}")
                report_lines.append(f"Critical Alerts:      {severity_counts['critical']}")
                report_lines.append(f"Warning Alerts:       {severity_counts['warning']}")
                report_lines.append(f"Info Alerts:          {severity_counts['info']}")
                
                # Recent critical alerts
                critical_alerts = [a for a in alerts[:10] if a.get('severity') == 'critical']
                if critical_alerts:
                    report_lines.append("")
                    report_lines.append("Recent Critical Alerts:")
                    for alert in critical_alerts[:5]:
                        timestamp = alert.get('timestamp', 'Unknown')
                        message = alert.get('message', 'No message')
                        report_lines.append(f"  • {timestamp}: {message}")
            else:
                report_lines.append("No alerts found for the specified period.")
                
        except Exception as e:
            report_lines.append(f"Error retrieving alerts: {e}")
        
        report_lines.append("")
        
        # Actions Summary
        report_lines.append("ACTIONS SUMMARY")
        report_lines.append("-" * 16)
        
        try:
            actions = self.db.get_action_history(limit=100)
            
            if actions:
                # Count by result
                result_counts = {"success": 0, "failed": 0, "pending": 0}
                action_types = {}
                
                for action in actions:
                    result = action.get('result', 'unknown')
                    if result in result_counts:
                        result_counts[result] += 1
                    
                    action_type = action.get('action_type', 'unknown')
                    action_types[action_type] = action_types.get(action_type, 0) + 1
                
                report_lines.append(f"Total Actions:        {len(actions)}")
                report_lines.append(f"Successful Actions:   {result_counts['success']}")
                report_lines.append(f"Failed Actions:       {result_counts['failed']}")
                report_lines.append(f"Pending Actions:      {result_counts['pending']}")
                
                # Most common actions
                if action_types:
                    report_lines.append("")
                    report_lines.append("Most Common Actions:")
                    sorted_actions = sorted(action_types.items(), key=lambda x: x[1], reverse=True)
                    for action_type, count in sorted_actions[:5]:
                        report_lines.append(f"  • {action_type}: {count} times")
            else:
                report_lines.append("No actions found for the specified period.")
                
        except Exception as e:
            report_lines.append(f"Error retrieving actions: {e}")
        
        report_lines.append("")
        
        # Health Status
        report_lines.append("SYSTEM HEALTH STATUS")
        report_lines.append("-" * 21)
        
        try:
            # Determine overall health based on recent metrics and alerts
            health_status = self._calculate_health_status(days)
            report_lines.append(f"Overall Health:       {health_status['status']}")
            report_lines.append(f"Health Score:         {health_status['score']}/100")
            report_lines.append("")
            report_lines.append("Health Factors:")
            for factor, value in health_status['factors'].items():
                report_lines.append(f"  • {factor}: {value}")
                
        except Exception as e:
            report_lines.append(f"Error calculating health status: {e}")
        
        report_lines.append("")
        report_lines.append("=" * 60)
        report_lines.append("End of Summary Report")
        report_lines.append("=" * 60)
        
        return "\n".join(report_lines)
    
    def _generate_detailed_report(self, days: int) -> str:
        """Generate a detailed report with charts and comprehensive data"""
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        report_lines = []
        report_lines.append("=" * 80)
        report_lines.append("AUTONOMOUS SYSTEM MANAGER - DETAILED REPORT")
        report_lines.append("=" * 80)
        report_lines.append(f"Report Period: {start_date.strftime('%Y-%m-%d %H:%M')} to {end_date.strftime('%Y-%m-%d %H:%M')}")
        report_lines.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append("")
        
        # System Configuration
        report_lines.append("CURRENT CONFIGURATION")
        report_lines.append("-" * 22)
        try:
            report_lines.append(f"CPU Threshold:        {getattr(self.config, 'CPU_THRESHOLD', 80)}%")
            report_lines.append(f"Memory Threshold:     {getattr(self.config, 'MEMORY_THRESHOLD', 85)}%")
            report_lines.append(f"Disk Threshold:       {getattr(self.config, 'DISK_THRESHOLD', 90)}%")
            report_lines.append(f"Monitoring Interval:  {getattr(self.config, 'MONITORING_INTERVAL', 60)} seconds")
            report_lines.append(f"Email Alerts:         {getattr(self.config, 'EMAIL_ALERTS', True)}")
            report_lines.append(f"Auto Actions:         {getattr(self.config, 'AUTO_ACTIONS', True)}")
        except Exception as e:
            report_lines.append(f"Error retrieving configuration: {e}")
        
        report_lines.append("")
        
        # Detailed Metrics Analysis
        report_lines.append("DETAILED METRICS ANALYSIS")
        report_lines.append("-" * 26)
        
        try:
            metrics_data = self.db.get_metrics_data(days=days)
            
            if metrics_data:
                # CPU Analysis
                cpu_values = [m.get('cpu', 0) for m in metrics_data]
                report_lines.append("CPU Usage Statistics:")
                report_lines.append(f"  Average: {sum(cpu_values)/len(cpu_values):.2f}%")
                report_lines.append(f"  Minimum: {min(cpu_values):.2f}%")
                report_lines.append(f"  Maximum: {max(cpu_values):.2f}%")
                report_lines.append(f"  Std Dev: {self._calculate_std_dev(cpu_values):.2f}%")
                
                # Generate ASCII chart for CPU
                cpu_chart = self._generate_ascii_chart(cpu_values, "CPU Usage (%)", width=60)
                report_lines.extend(cpu_chart)
                report_lines.append("")
                
                # Memory Analysis
                memory_values = [m.get('memory', 0) for m in metrics_data]
                report_lines.append("Memory Usage Statistics:")
                report_lines.append(f"  Average: {sum(memory_values)/len(memory_values):.2f}%")
                report_lines.append(f"  Minimum: {min(memory_values):.2f}%")
                report_lines.append(f"  Maximum: {max(memory_values):.2f}%")
                report_lines.append(f"  Std Dev: {self._calculate_std_dev(memory_values):.2f}%")
                
                # Generate ASCII chart for Memory
                memory_chart = self._generate_ascii_chart(memory_values, "Memory Usage (%)", width=60)
                report_lines.extend(memory_chart)
                report_lines.append("")
                
                # Disk Analysis
                disk_values = [m.get('disk', 0) for m in metrics_data]
                report_lines.append("Disk Usage Statistics:")
                report_lines.append(f"  Average: {sum(disk_values)/len(disk_values):.2f}%")
                report_lines.append(f"  Minimum: {min(disk_values):.2f}%")
                report_lines.append(f"  Maximum: {max(disk_values):.2f}%")
                report_lines.append(f"  Std Dev: {self._calculate_std_dev(disk_values):.2f}%")
                
                # Generate ASCII chart for Disk
                disk_chart = self._generate_ascii_chart(disk_values, "Disk Usage (%)", width=60)
                report_lines.extend(disk_chart)
                report_lines.append("")
                
            else:
                report_lines.append("No metrics data available.")
                
        except Exception as e:
            report_lines.append(f"Error analyzing metrics: {e}")
        
        # Detailed Alerts Analysis
        report_lines.append("DETAILED ALERTS ANALYSIS")
        report_lines.append("-" * 25)
        
        try:
            alerts = self.db.get_recent_alerts(limit=200)
            
            if alerts:
                # Alert frequency analysis
                alert_frequency = self._analyze_alert_frequency(alerts, days)
                report_lines.append(f"Alerts per day: {alert_frequency:.1f}")
                
                # Alerts by hour
                hourly_distribution = self._analyze_hourly_distribution(alerts)
                report_lines.append("")
                report_lines.append("Alerts by Hour of Day:")
                for hour, count in sorted(hourly_distribution.items()):
                    bar = "█" * (count // max(1, max(hourly_distribution.values()) // 20))
                    report_lines.append(f"  {hour:02d}:00 │{bar} {count}")
                
                report_lines.append("")
                
                # Recent alerts table
                report_lines.append("Recent Alerts (Last 10):")
                report_lines.append("Timestamp           | Severity | Message")
                report_lines.append("-" * 70)
                for alert in alerts[:10]:
                    timestamp = alert.get('timestamp', 'Unknown')[:19]
                    severity = alert.get('severity', 'info')[:8]
                    message = alert.get('message', 'No message')[:40]
                    report_lines.append(f"{timestamp} | {severity:8} | {message}")
                    
            else:
                report_lines.append("No alerts found.")
                
        except Exception as e:
            report_lines.append(f"Error analyzing alerts: {e}")
        
        report_lines.append("")
        
        # Detailed Actions Analysis
        report_lines.append("DETAILED ACTIONS ANALYSIS")
        report_lines.append("-" * 26)
        
        try:
            actions = self.db.get_action_history(limit=200)
            
            if actions:
                # Success rate analysis
                success_rate = self._calculate_success_rate(actions)
                report_lines.append(f"Action Success Rate: {success_rate:.1f}%")
                
                # Actions by type
                action_types = {}
                for action in actions:
                    action_type = action.get('action_type', 'unknown')
                    action_types[action_type] = action_types.get(action_type, 0) + 1
                
                report_lines.append("")
                report_lines.append("Actions by Type:")
                for action_type, count in sorted(action_types.items(), key=lambda x: x[1], reverse=True):
                    bar = "█" * (count // max(1, max(action_types.values()) // 20))
                    report_lines.append(f"  {action_type:20} │{bar} {count}")
                
                report_lines.append("")
                
                # Recent actions table
                report_lines.append("Recent Actions (Last 10):")
                report_lines.append("Timestamp           | Action           | Target      | Result")
                report_lines.append("-" * 70)
                for action in actions[:10]:
                    timestamp = action.get('timestamp', 'Unknown')[:19]
                    action_type = action.get('action_type', 'unknown')[:15]
                    target = action.get('target', 'N/A')[:10]
                    result = action.get('result', 'unknown')[:10]
                    report_lines.append(f"{timestamp} | {action_type:15} | {target:10} | {result}")
                    
            else:
                report_lines.append("No actions found.")
                
        except Exception as e:
            report_lines.append(f"Error analyzing actions: {e}")
        
        report_lines.append("")
        report_lines.append("=" * 80)
        report_lines.append("End of Detailed Report")
        report_lines.append("=" * 80)
        
        return "\n".join(report_lines)
    
    def _generate_performance_report(self, days: int) -> str:
        """Generate a performance-focused report"""
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        report_lines = []
        report_lines.append("=" * 70)
        report_lines.append("AUTONOMOUS SYSTEM MANAGER - PERFORMANCE REPORT")
        report_lines.append("=" * 70)
        report_lines.append(f"Report Period: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
        report_lines.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append("")
        
        try:
            metrics_data = self.db.get_metrics_data(days=days)
            
            if not metrics_data:
                report_lines.append("No performance data available.")
                return "\n".join(report_lines)
            
            # Performance Trends
            report_lines.append("PERFORMANCE TRENDS")
            report_lines.append("-" * 18)
            
            # Calculate trends
            cpu_trend = self._calculate_trend([m.get('cpu', 0) for m in metrics_data])
            memory_trend = self._calculate_trend([m.get('memory', 0) for m in metrics_data])
            disk_trend = self._calculate_trend([m.get('disk', 0) for m in metrics_data])
            
            report_lines.append(f"CPU Usage Trend:      {cpu_trend}")
            report_lines.append(f"Memory Usage Trend:   {memory_trend}")
            report_lines.append(f"Disk Usage Trend:     {disk_trend}")
            report_lines.append("")
            
            # Peak Performance Periods
            report_lines.append("PEAK PERFORMANCE ANALYSIS")
            report_lines.append("-" * 26)
            
            peak_periods = self._identify_peak_periods(metrics_data)
            if peak_periods:
                report_lines.append("High Resource Usage Periods:")
                for period in peak_periods[:5]:
                    report_lines.append(f"  • {period['timestamp']}: CPU {period['cpu']:.1f}%, "
                                      f"Memory {period['memory']:.1f}%, Disk {period['disk']:.1f}%")
            else:
                report_lines.append("No significant peak periods identified.")
            
            report_lines.append("")
            
            # Resource Utilization Efficiency
            report_lines.append("RESOURCE UTILIZATION EFFICIENCY")
            report_lines.append("-" * 32)
            
            efficiency = self._calculate_efficiency(metrics_data)
            report_lines.append(f"CPU Efficiency:       {efficiency['cpu']:.1f}%")
            report_lines.append(f"Memory Efficiency:    {efficiency['memory']:.1f}%")
            report_lines.append(f"Disk Efficiency:      {efficiency['disk']:.1f}%")
            report_lines.append(f"Overall Efficiency:   {efficiency['overall']:.1f}%")
            report_lines.append("")
            
            # Performance Recommendations
            report_lines.append("PERFORMANCE RECOMMENDATIONS")
            report_lines.append("-" * 28)
            
            recommendations = self._generate_performance_recommendations(metrics_data)
            for recommendation in recommendations:
                report_lines.append(f"• {recommendation}")
            
            report_lines.append("")
            
            # System Load Patterns
            report_lines.append("SYSTEM LOAD PATTERNS")
            report_lines.append("-" * 21)
            
            load_patterns = self._analyze_load_patterns(metrics_data)
            report_lines.append("Daily Load Pattern (Average by Hour):")
            for hour, avg_load in sorted(load_patterns.items()):
                bar_length = int(avg_load / 5)  # Scale for display
                bar = "█" * bar_length
                report_lines.append(f"  {hour:02d}:00 │{bar:20} {avg_load:.1f}%")
            
        except Exception as e:
            report_lines.append(f"Error generating performance report: {e}")
        
        report_lines.append("")
        report_lines.append("=" * 70)
        report_lines.append("End of Performance Report")
        report_lines.append("=" * 70)
        
        return "\n".join(report_lines)
    
    def export_data(self, format_type: str = "csv", days: int = 7, filename: Optional[str] = None) -> Dict[str, Any]:
        """Export system data in specified format"""
        try:
            if not filename:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"{self.reports_dir}/system_data_{timestamp}.{format_type}"
            
            # Get data from database
            metrics_data = self.db.get_metrics_data(days=days)
            
            if not metrics_data:
                return {"success": False, "error": "No data to export"}
            
            if format_type.lower() == "csv":
                result = self._export_to_csv(metrics_data, filename)
            elif format_type.lower() == "json":
                result = self._export_to_json(metrics_data, filename)
            else:
                return {"success": False, "error": f"Unsupported format: {format_type}"}
            
            result["record_count"] = len(metrics_data)
            return result
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _export_to_csv(self, data: List[Dict], filename: str) -> Dict[str, Any]:
        """Export data to CSV format"""
        try:
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                if not data:
                    return {"success": False, "error": "No data to export"}
                
                fieldnames = data[0].keys()
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for row in data:
                    writer.writerow(row)
            
            return {"success": True, "filename": filename}
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _export_to_json(self, data: List[Dict], filename: str) -> Dict[str, Any]:
        """Export data to JSON format"""
        try:
            export_data = {
                "export_timestamp": datetime.now().isoformat(),
                "record_count": len(data),
                "data": data
            }
            
            with open(filename, 'w', encoding='utf-8') as jsonfile:
                json.dump(export_data, jsonfile, indent=2, default=str)
            
            return {"success": True, "filename": filename}
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _calculate_health_status(self, days: int) -> Dict[str, Any]:
        """Calculate overall system health status"""
        try:
            metrics_data = self.db.get_metrics_data(days=days)
            alerts = self.db.get_recent_alerts(limit=100)
            actions = self.db.get_action_history(limit=100)
            
            health_score = 100
            factors = {}
            
            if metrics_data:
                # CPU health factor
                cpu_avg = sum(m.get('cpu', 0) for m in metrics_data) / len(metrics_data)
                cpu_penalty = max(0, (cpu_avg - 70) * 2)  # Penalty for high CPU
                health_score -= cpu_penalty
                factors["CPU Usage"] = f"{cpu_avg:.1f}% avg (-{cpu_penalty:.0f} points)"
                
                # Memory health factor
                memory_avg = sum(m.get('memory', 0) for m in metrics_data) / len(metrics_data)
                memory_penalty = max(0, (memory_avg - 80) * 2)  # Penalty for high memory
                health_score -= memory_penalty
                factors["Memory Usage"] = f"{memory_avg:.1f}% avg (-{memory_penalty:.0f} points)"
                
                # Disk health factor
                disk_avg = sum(m.get('disk', 0) for m in metrics_data) / len(metrics_data)
                disk_penalty = max(0, (disk_avg - 85) * 3)  # Higher penalty for disk
                health_score -= disk_penalty
                factors["Disk Usage"] = f"{disk_avg:.1f}% avg (-{disk_penalty:.0f} points)"
            
            # Alert penalty
            if alerts:
                critical_count = sum(1 for a in alerts if a.get('severity') == 'critical')
                warning_count = sum(1 for a in alerts if a.get('severity') == 'warning')
                alert_penalty = critical_count * 5 + warning_count * 2
                health_score -= alert_penalty
                factors["Alerts"] = f"{len(alerts)} total, {critical_count} critical (-{alert_penalty:.0f} points)"
            
            # Action success rate
            if actions:
                success_rate = self._calculate_success_rate(actions)
                action_penalty = max(0, (100 - success_rate) * 0.5)
                health_score -= action_penalty
                factors["Action Success"] = f"{success_rate:.1f}% (-{action_penalty:.0f} points)"
            
            health_score = max(0, min(100, health_score))
            
            # Determine status
            if health_score >= 90:
                status = "Excellent"
            elif health_score >= 75:
                status = "Good"
            elif health_score >= 60:
                status = "Fair"
            elif health_score >= 40:
                status = "Poor"
            else:
                status = "Critical"
            
            return {
                "status": status,
                "score": int(health_score),
                "factors": factors
            }
            
        except Exception as e:
            return {
                "status": "Unknown",
                "score": 0,
                "factors": {"Error": str(e)}
            }
    
    def _calculate_std_dev(self, values: List[float]) -> float:
        """Calculate standard deviation"""
        if len(values) < 2:
            return 0.0
        
        mean = sum(values) / len(values)
        variance = sum((x - mean) ** 2 for x in values) / len(values)
        return variance ** 0.5
    
    def _generate_ascii_chart(self, values: List[float], title: str, width: int = 60) -> List[str]:
        """Generate ASCII chart for values"""
        if not values:
            return [f"{title}: No data available"]
        
        chart_lines = []
        chart_lines.append(f"{title}:")
        
        # Normalize values for chart display
        max_val = max(values)
        min_val = min(values)
        range_val = max_val - min_val if max_val != min_val else 1
        
        # Create chart
        chart_height = 10
        for i in range(chart_height, 0, -1):
            line = f"{(min_val + (i/chart_height) * range_val):5.1f} │"
            for val in values[-width:]:  # Show last 'width' values
                normalized = (val - min_val) / range_val
                if normalized >= (i - 1) / chart_height:
                    line += "█"
                else:
                    line += " "
            chart_lines.append(line)
        
        # Add x-axis
        chart_lines.append("      └" + "─" * min(len(values), width))
        
        return chart_lines
    
    def _analyze_alert_frequency(self, alerts: List[Dict], days: int) -> float:
        """Analyze alert frequency per day"""
        if not alerts or days == 0:
            return 0.0
        return len(alerts) / days
    
    def _analyze_hourly_distribution(self, alerts: List[Dict]) -> Dict[int, int]:
        """Analyze alerts by hour of day"""
        hourly_dist = {i: 0 for i in range(24)}
        
        for alert in alerts:
            try:
                timestamp_str = alert.get('timestamp', '')
                if timestamp_str:
                    # Extract hour from timestamp (assuming format: YYYY-MM-DD HH:MM:SS)
                    hour = int(timestamp_str.split(' ')[1].split(':')[0])
                    hourly_dist[hour] += 1
            except (IndexError, ValueError):
                continue
        
        return hourly_dist
    
    def _calculate_success_rate(self, actions: List[Dict]) -> float:
        """Calculate action success rate"""
        if not actions:
            return 100.0
        
        successful = sum(1 for action in actions if action.get('result') == 'success')
        return (successful / len(actions)) * 100
    
    def _calculate_trend(self, values: List[float]) -> str:
        """Calculate trend direction"""
        if len(values) < 2:
            return "Insufficient data"
        
        # Simple linear trend calculation
        n = len(values)
        x_sum = sum(range(n))
        y_sum = sum(values)
        xy_sum = sum(i * values[i] for i in range(n))
        x_sq_sum = sum(i * i for i in range(n))
        
        slope = (n * xy_sum - x_sum * y_sum) / (n * x_sq_sum - x_sum * x_sum)
        
        if slope > 0.1:
            return "Increasing ↗"
        elif slope < -0.1:
            return "Decreasing ↘"
        else:
            return "Stable →"
    
    def _identify_peak_periods(self, metrics_data: List[Dict]) -> List[Dict]:
        """Identify periods of high resource usage"""
        peaks = []
        
        for metric in metrics_data:
            cpu = metric.get('cpu', 0)
            memory = metric.get('memory', 0)
            disk = metric.get('disk', 0)
            
            # Consider it a peak if any metric is above 85%
            if cpu > 85 or memory > 85 or disk > 85:
                peaks.append({
                    'timestamp': metric.get('timestamp', 'Unknown'),
                    'cpu': cpu,
                    'memory': memory,
                    'disk': disk
                })
        
        return sorted(peaks, key=lambda x: x['cpu'] + x['memory'] + x['disk'], reverse=True)
    
    def _calculate_efficiency(self, metrics_data: List[Dict]) -> Dict[str, float]:
        """Calculate resource utilization efficiency"""
        if not metrics_data:
            return {"cpu": 0, "memory": 0, "disk": 0, "overall": 0}
        
        # Efficiency is inverse of waste (100% - average usage)
        cpu_avg = sum(m.get('cpu', 0) for m in metrics_data) / len(metrics_data)
        memory_avg = sum(m.get('memory', 0) for m in metrics_data) / len(metrics_data)
        disk_avg = sum(m.get('disk', 0) for m in metrics_data) / len(metrics_data)
        
        # Efficiency score: optimal usage is around 70-80%
        cpu_eff = max(0, 100 - abs(cpu_avg - 75))
        memory_eff = max(0, 100 - abs(memory_avg - 75))
        disk_eff = max(0, 100 - abs(disk_avg - 50))  # Disk should be lower
        
        overall_eff = (cpu_eff + memory_eff + disk_eff) / 3
        
        return {
            "cpu": cpu_eff,
            "memory": memory_eff,
            "disk": disk_eff,
            "overall": overall_eff
        }
    
    def _generate_performance_recommendations(self, metrics_data: List[Dict]) -> List[str]:
        """Generate performance recommendations"""
        recommendations = []
        
        if not metrics_data:
            return ["No data available for recommendations"]
        
        cpu_avg = sum(m.get('cpu', 0) for m in metrics_data) / len(metrics_data)
        memory_avg = sum(m.get('memory', 0) for m in metrics_data) / len(metrics_data)
        disk_avg = sum(m.get('disk', 0) for m in metrics_data) / len(metrics_data)
        
        # CPU recommendations
        if cpu_avg > 80:
            recommendations.append("Consider optimizing CPU-intensive processes or upgrading CPU")
        elif cpu_avg < 30:
            recommendations.append("CPU utilization is low - consider consolidating workloads")
        
        # Memory recommendations
        if memory_avg > 85:
            recommendations.append("Memory usage is high - consider adding more RAM or optimizing memory usage")
        elif memory_avg < 40:
            recommendations.append("Memory utilization is low - current capacity may be oversized")
        
        # Disk recommendations
        if disk_avg > 90:
            recommendations.append("Disk space is critically low - immediate cleanup or expansion required")
        elif disk_avg > 80:
            recommendations.append("Disk usage is high - plan for capacity expansion")
        
        # General recommendations
        cpu_variance = self._calculate_std_dev([m.get('cpu', 0) for m in metrics_data])
        if cpu_variance > 20:
            recommendations.append("High CPU variance detected - investigate sporadic high-usage processes")
        
        if not recommendations:
            recommendations.append("System performance appears optimal - no immediate action required")
        
        return recommendations
    
    def _analyze_load_patterns(self, metrics_data: List[Dict]) -> Dict[int, float]:
        """Analyze system load patterns by hour"""
        hourly_loads = {i: [] for i in range(24)}
        
        for metric in metrics_data:
            try:
                timestamp_str = metric.get('timestamp', '')
                if timestamp_str:
                    hour = int(timestamp_str.split(' ')[1].split(':')[0])
                    # Calculate combined load (weighted average)
                    load = (metric.get('cpu', 0) * 0.4 + 
                           metric.get('memory', 0) * 0.4 + 
                           metric.get('disk', 0) * 0.2)
                    hourly_loads[hour].append(load)
            except (IndexError, ValueError):
                continue
        
        # Calculate averages
        hourly_averages = {}
        for hour, loads in hourly_loads.items():
            if loads:
                hourly_averages[hour] = sum(loads) / len(loads)
            else:
                hourly_averages[hour] = 0.0
        
        return hourly_averages

# Helper function for standalone usage
def generate_report_cli(report_type: str = "summary", days: int = 7):
    """CLI function to generate reports"""
    generator = ReportGenerator()
    result = generator.generate_report(report_type, days)
    
    if result["success"]:
        print(f"Report generated successfully: {result['filename']}")
    else:
        print(f"Error generating report: {result['error']}")

if __name__ == "__main__":
    # Example usage
    import sys
    
    if len(sys.argv) > 1:
        report_type = sys.argv[1]
        days = int(sys.argv[2]) if len(sys.argv) > 2 else 7
        generate_report_cli(report_type, days)
    else:
        print("Usage: python reports.py <report_type> [days]")
        print("Report types: summary, detailed, performance")