#!/usr/bin/env python3
"""
Test script for daemon mode functionality
"""

import time
import threading
import signal
import sys
from main import AutonomousSystemManager

def test_daemon_mode():
    """Test daemon mode with shorter intervals"""
    print("🔄 Testing Daemon Mode")
    print("=" * 50)
    
    # Create a system manager with shorter intervals for testing
    try:
        manager = AutonomousSystemManager()
        
        # Override the monitoring interval for testing
        manager.config.MONITORING_INTERVAL = 5  # 5 seconds instead of 60
        
        print("✅ SystemManager created successfully")
        print(f"📊 Monitoring interval: {manager.config.MONITORING_INTERVAL} seconds")
        
        # Start monitoring
        print("🚀 Starting daemon monitoring...")
        manager.start_monitoring()
        
        # Let it run for 15 seconds to collect some data
        print("⏱️  Running for 15 seconds to collect data...")
        time.sleep(15)
        
        # Stop monitoring
        print("🛑 Stopping monitoring...")
        manager.stop_monitoring()
        
        # Check if data was collected
        print("📊 Checking collected data...")
        recent_metrics = manager.db.get_recent_metrics(hours=1)
        print(f"✅ Found {len(recent_metrics)} metrics in database")
        
        if recent_metrics:
            print("📈 Sample metrics:")
            for metric in recent_metrics[-3:]:  # Show last 3 metrics
                print(f"   - {metric.get('metric_type', 'unknown')}: {metric.get('value', 'N/A')} at {metric.get('timestamp', 'unknown time')}")
        
        # Check alerts
        recent_alerts = manager.db.get_recent_alerts(hours=1)
        print(f"🚨 Found {len(recent_alerts)} alerts")
        
        print("\n✅ Daemon mode test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error testing daemon mode: {e}")
        import traceback
        traceback.print_exc()
        return False

def signal_handler(sig, frame):
    """Handle Ctrl+C gracefully"""
    print("\n🛑 Test interrupted by user")
    sys.exit(0)

if __name__ == "__main__":
    # Set up signal handler for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)
    
    try:
        success = test_daemon_mode()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted")
        sys.exit(0)
