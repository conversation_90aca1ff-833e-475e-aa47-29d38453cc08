# https://sw.kovidgoyal.net/kitty/keyboard-protocol/#functional-key-definitions
FUNCTIONAL_KEYS = {
    "27u": "escape",
    "13u": "enter",
    "9u": "tab",
    "127u": "backspace",
    "2~": "insert",
    "3~": "delete",
    "1D": "left",
    "1C": "right",
    "1A": "up",
    "1B": "down",
    "5~": "pageup",
    "6~": "pagedown",
    "1H": "home",
    "7~": "home",
    "1F": "end",
    "8~": "end",
    "57358u": "caps_lock",
    "57359u": "scroll_lock",
    "57360u": "num_lock",
    "57361u": "print_screen",
    "57362u": "pause",
    "57363u": "menu",
    "1P": "f1",
    "11~": "f1",
    "1Q": "f2",
    "12~": "f2",
    "13~": "f3",
    "1R": "f3",
    "1S": "f4",
    "14~": "f4",
    "15~": "f5",
    "17~": "f6",
    "18~": "f7",
    "19~": "f8",
    "20~": "f9",
    "21~": "f10",
    "23~": "f11",
    "24~": "f12",
    "57376u": "f13",
    "57377u": "f14",
    "57378u": "f15",
    "57379u": "f16",
    "57380u": "f17",
    "57381u": "f18",
    "57382u": "f19",
    "57383u": "f20",
    "57384u": "f21",
    "57385u": "f22",
    "57386u": "f23",
    "57387u": "f24",
    "57388u": "f25",
    "57389u": "f26",
    "57390u": "f27",
    "57391u": "f28",
    "57392u": "f29",
    "57393u": "f30",
    "57394u": "f31",
    "57395u": "f32",
    "57396u": "f33",
    "57397u": "f34",
    "57398u": "f35",
    "57399u": "0",
    "57400u": "1",
    "57401u": "2",
    "57402u": "3",
    "57403u": "4",
    "57404u": "5",
    "57405u": "6",
    "57406u": "7",
    "57407u": "8",
    "57408u": "9",
    "57409u": "decimal",
    "57410u": "divide",
    "57411u": "multiply",
    "57412u": "subtract",
    "57413u": "add",
    "57414u": "enter",
    "57415u": "equal",
    "57416u": "separator",
    "57417u": "left",
    "57418u": "right",
    "57419u": "up",
    "57420u": "down",
    "57421u": "pageup",
    "57422u": "pagedown",
    "57423u": "home",
    "57424u": "end",
    "57425u": "insert",
    "57426u": "delete",
    "1E": "kp_begin",
    "57427~": "kp_begin",
    "57428u": "media_play",
    "57429u": "media_pause",
    "57430u": "media_play_pause",
    "57431u": "media_reverse",
    "57432u": "media_stop",
    "57433u": "media_fast_forward",
    "57434u": "media_rewind",
    "57435u": "media_track_next",
    "57436u": "media_track_previous",
    "57437u": "media_record",
    "57438u": "lower_volume",
    "57439u": "raise_volume",
    "57440u": "mute_volume",
    "57441u": "left_shift",
    "57442u": "left_control",
    "57443u": "left_alt",
    "57444u": "left_super",
    "57445u": "left_hyper",
    "57446u": "left_meta",
    "57447u": "right_shift",
    "57448u": "right_control",
    "57449u": "right_alt",
    "57450u": "right_super",
    "57451u": "right_hyper",
    "57452u": "right_meta",
    "57453u": "iso_level3_shift",
    "57454u": "iso_level5_shift",
}
