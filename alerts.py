import smtplib
import logging
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
import sqlite3
from dataclasses import dataclass
from enum import Enum

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AlertSeverity(Enum):
    """Alert severity levels."""
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    CRITICAL = "CRITICAL"

class AlertStatus(Enum):
    """Alert status types."""
    ACTIVE = "ACTIVE"
    ACKNOWLEDGED = "ACKNOWLEDGED"
    RESOLVED = "RESOLVED"
    SUPPRESSED = "SUPPRESSED"

@dataclass
class Alert:
    """Alert data structure."""
    id: str
    timestamp: datetime
    severity: AlertSeverity
    status: AlertStatus
    title: str
    message: str
    source: str
    metric_name: str
    metric_value: float
    threshold: float
    tags: List[str]
    action_taken: Optional[str] = None
    acknowledged_by: Optional[str] = None
    resolved_at: Optional[datetime] = None

class AlertManager:
    """Manages system alerts, notifications, and alert lifecycle."""
    
    def __init__(self, config: Dict, db_path: str = "alerts.db"):
        self.config = config
        self.db_path = db_path
        self.active_alerts: Dict[str, Alert] = {}
        self.alert_history: List[Alert] = []
        self.suppressed_alerts: Set[str] = set()
        
        # Email configuration
        self.smtp_server = config.get('SMTP_SERVER', 'localhost')
        self.smtp_port = config.get('SMTP_PORT', 587)
        self.smtp_username = config.get('SMTP_USERNAME', '')
        self.smtp_password = config.get('SMTP_PASSWORD', '')
        self.smtp_use_tls = config.get('SMTP_USE_TLS', True)
        self.from_email = config.get('FROM_EMAIL', '<EMAIL>')
        self.to_emails = config.get('TO_EMAILS', [])
        
        # Alert throttling configuration
        self.alert_cooldown = config.get('ALERT_COOLDOWN_MINUTES', 15)
        self.max_alerts_per_hour = config.get('MAX_ALERTS_PER_HOUR', 20)
        
        # Initialize database
        self.init_database()
        self.load_active_alerts()
        
    def init_database(self):
        """Initialize the alerts database."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS alerts (
                    id TEXT PRIMARY KEY,
                    timestamp TEXT NOT NULL,
                    severity TEXT NOT NULL,
                    status TEXT NOT NULL,
                    title TEXT NOT NULL,
                    message TEXT NOT NULL,
                    source TEXT NOT NULL,
                    metric_name TEXT NOT NULL,
                    metric_value REAL NOT NULL,
                    threshold REAL NOT NULL,
                    tags TEXT,
                    action_taken TEXT,
                    acknowledged_by TEXT,
                    resolved_at TEXT
                )
            ''')
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS alert_suppressions (
                    alert_pattern TEXT PRIMARY KEY,
                    suppressed_until TEXT NOT NULL,
                    created_by TEXT,
                    reason TEXT
                )
            ''')
            
            conn.commit()
            conn.close()
            logger.info("Alert database initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize alert database: {str(e)}")
    
    def load_active_alerts(self):
        """Load active alerts from database."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT * FROM alerts 
                WHERE status IN ('ACTIVE', 'ACKNOWLEDGED')
                ORDER BY timestamp DESC
            ''')
            
            rows = cursor.fetchall()
            for row in rows:
                alert = self._row_to_alert(row)
                self.active_alerts[alert.id] = alert
                
            conn.close()
            logger.info(f"Loaded {len(self.active_alerts)} active alerts")
            
        except Exception as e:
            logger.error(f"Failed to load active alerts: {str(e)}")
    
    def _row_to_alert(self, row) -> Alert:
        """Convert database row to Alert object."""
        return Alert(
            id=row[0],
            timestamp=datetime.fromisoformat(row[1]),
            severity=AlertSeverity(row[2]),
            status=AlertStatus(row[3]),
            title=row[4],
            message=row[5],
            source=row[6],
            metric_name=row[7],
            metric_value=row[8],
            threshold=row[9],
            tags=json.loads(row[10]) if row[10] else [],
            action_taken=row[11],
            acknowledged_by=row[12],
            resolved_at=datetime.fromisoformat(row[13]) if row[13] else None
        )
    
    def _alert_to_row(self, alert: Alert) -> tuple:
        """Convert Alert object to database row."""
        return (
            alert.id,
            alert.timestamp.isoformat(),
            alert.severity.value,
            alert.status.value,
            alert.title,
            alert.message,
            alert.source,
            alert.metric_name,
            alert.metric_value,
            alert.threshold,
            json.dumps(alert.tags),
            alert.action_taken,
            alert.acknowledged_by,
            alert.resolved_at.isoformat() if alert.resolved_at else None
        )
    
    def create_alert(self, 
                    title: str,
                    message: str,
                    severity: AlertSeverity,
                    source: str,
                    metric_name: str,
                    metric_value: float,
                    threshold: float,
                    tags: List[str] = None) -> Optional[Alert]:
        """Create a new alert."""
        
        if tags is None:
            tags = []
        
        # Generate unique alert ID
        alert_id = f"{source}_{metric_name}_{int(time.time())}"
        
        # Check if similar alert is suppressed
        if self._is_alert_suppressed(alert_id, source, metric_name):
            logger.info(f"Alert suppressed: {alert_id}")
            return None
        
        # Check alert rate limiting
        if self._is_rate_limited():
            logger.warning("Alert rate limit exceeded, skipping alert creation")
            return None
        
        # Create alert object
        alert = Alert(
            id=alert_id,
            timestamp=datetime.now(),
            severity=severity,
            status=AlertStatus.ACTIVE,
            title=title,
            message=message,
            source=source,
            metric_name=metric_name,
            metric_value=metric_value,
            threshold=threshold,
            tags=tags
        )
        
        # Store in database
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO alerts VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', self._alert_to_row(alert))
            
            conn.commit()
            conn.close()
            
            # Add to active alerts
            self.active_alerts[alert.id] = alert
            self.alert_history.append(alert)
            
            logger.info(f"Alert created: {alert.id} - {alert.title}")
            
            # Send notifications
            self._send_notifications(alert)
            
            return alert
            
        except Exception as e:
            logger.error(f"Failed to create alert: {str(e)}")
            return None
    
    def _is_alert_suppressed(self, alert_id: str, source: str, metric_name: str) -> bool:
        """Check if alert should be suppressed due to recent similar alerts."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Check for recent similar alerts
            cutoff_time = datetime.now() - timedelta(minutes=self.alert_cooldown)
            cursor.execute('''
                SELECT COUNT(*) FROM alerts 
                WHERE source = ? AND metric_name = ? 
                AND timestamp > ? AND status = 'ACTIVE'
            ''', (source, metric_name, cutoff_time.isoformat()))
            
            count = cursor.fetchone()[0]
            conn.close()
            
            return count > 0
            
        except Exception as e:
            logger.error(f"Error checking alert suppression: {str(e)}")
            return False
    
    def _is_rate_limited(self) -> bool:
        """Check if alert creation is rate limited."""
        cutoff_time = datetime.now() - timedelta(hours=1)
        recent_alerts = [
            alert for alert in self.alert_history
            if alert.timestamp > cutoff_time
        ]
        return len(recent_alerts) >= self.max_alerts_per_hour
    
    def _send_notifications(self, alert: Alert):
        """Send alert notifications via configured channels."""
        try:
            # Send email notifications
            if self.config.get('EMAIL_ALERTS', True) and self.to_emails:
                self._send_email_notification(alert)
            
            # Log alert for other notification systems
            logger.info(f"ALERT [{alert.severity.value}]: {alert.title}")
            logger.info(f"Message: {alert.message}")
            logger.info(f"Metric: {alert.metric_name} = {alert.metric_value} (threshold: {alert.threshold})")
            
        except Exception as e:
            logger.error(f"Error sending notifications for alert {alert.id}: {str(e)}")
    
    def _send_email_notification(self, alert: Alert):
        """Send email notification for alert."""
        try:
            # Create message
            msg = MIMEMultipart()
            msg['From'] = self.from_email
            msg['To'] = ', '.join(self.to_emails)
            msg['Subject'] = f"[{alert.severity.value}] System Alert: {alert.title}"
            
            # Create HTML body
            html_body = self._create_email_html(alert)
            msg.attach(MIMEText(html_body, 'html'))
            
            # Send email
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                if self.smtp_use_tls:
                    server.starttls()
                if self.smtp_username and self.smtp_password:
                    server.login(self.smtp_username, self.smtp_password)
                
                server.send_message(msg)
            
            logger.info(f"Email notification sent for alert: {alert.id}")
            
        except Exception as e:
            logger.error(f"Failed to send email notification: {str(e)}")
    
    def _create_email_html(self, alert: Alert) -> str:
        """Create HTML email body for alert."""
        severity_colors = {
            AlertSeverity.LOW: "#28a745",
            AlertSeverity.MEDIUM: "#ffc107",
            AlertSeverity.HIGH: "#fd7e14",
            AlertSeverity.CRITICAL: "#dc3545"
        }
        
        color = severity_colors.get(alert.severity, "#6c757d")
        
        html = f"""
        <html>
        <head>
            <style>
                .alert-container {{ 
                    font-family: Arial, sans-serif; 
                    max-width: 600px; 
                    margin: 0 auto; 
                    border: 2px solid {color};
                    border-radius: 8px;
                    padding: 20px;
                }}
                .alert-header {{ 
                    background-color: {color}; 
                    color: white; 
                    padding: 15px; 
                    margin: -20px -20px 20px -20px;
                    border-radius: 6px 6px 0 0;
                }}
                .alert-title {{ margin: 0; font-size: 24px; }}
                .alert-info {{ background-color: #f8f9fa; padding: 15px; border-radius: 4px; margin: 10px 0; }}
                .metric-value {{ font-size: 18px; font-weight: bold; color: {color}; }}
                .timestamp {{ color: #6c757d; font-size: 14px; }}
            </style>
        </head>
        <body>
            <div class="alert-container">
                <div class="alert-header">
                    <h1 class="alert-title">[{alert.severity.value}] {alert.title}</h1>
                </div>
                
                <div class="alert-info">
                    <p><strong>Message:</strong> {alert.message}</p>
                    <p><strong>Source:</strong> {alert.source}</p>
                    <p><strong>Metric:</strong> {alert.metric_name}</p>
                    <p><strong>Current Value:</strong> <span class="metric-value">{alert.metric_value}</span></p>
                    <p><strong>Threshold:</strong> {alert.threshold}</p>
                    <p><strong>Alert ID:</strong> {alert.id}</p>
                    <p class="timestamp"><strong>Time:</strong> {alert.timestamp.strftime('%Y-%m-%d %H:%M:%S')}</p>
                </div>
                
                {"<p><strong>Tags:</strong> " + ", ".join(alert.tags) + "</p>" if alert.tags else ""}
                
                <hr>
                <p style="font-size: 12px; color: #6c757d;">
                    This alert was generated by the Autonomous System Manager. 
                    Please investigate and take appropriate action if necessary.
                </p>
            </div>
        </body>
        </html>
        """
        return html
    
    def acknowledge_alert(self, alert_id: str, acknowledged_by: str) -> bool:
        """Acknowledge an active alert."""
        try:
            if alert_id not in self.active_alerts:
                logger.warning(f"Alert {alert_id} not found in active alerts")
                return False
            
            alert = self.active_alerts[alert_id]
            alert.status = AlertStatus.ACKNOWLEDGED
            alert.acknowledged_by = acknowledged_by
            
            # Update database
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                UPDATE alerts 
                SET status = ?, acknowledged_by = ?
                WHERE id = ?
            ''', (alert.status.value, acknowledged_by, alert_id))
            
            conn.commit()
            conn.close()
            
            logger.info(f"Alert {alert_id} acknowledged by {acknowledged_by}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to acknowledge alert {alert_id}: {str(e)}")
            return False
    
    def resolve_alert(self, alert_id: str, resolved_by: str = "system") -> bool:
        """Resolve an active alert."""
        try:
            if alert_id not in self.active_alerts:
                logger.warning(f"Alert {alert_id} not found in active alerts")
                return False
            
            alert = self.active_alerts[alert_id]
            alert.status = AlertStatus.RESOLVED
            alert.resolved_at = datetime.now()
            
            # Update database
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                UPDATE alerts 
                SET status = ?, resolved_at = ?
                WHERE id = ?
            ''', (alert.status.value, alert.resolved_at.isoformat(), alert_id))
            
            conn.commit()
            conn.close()
            
            # Remove from active alerts
            del self.active_alerts[alert_id]
            
            logger.info(f"Alert {alert_id} resolved by {resolved_by}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to resolve alert {alert_id}: {str(e)}")
            return False
    
    def suppress_alert_type(self, source: str, metric_name: str, 
                           duration_minutes: int, reason: str = "") -> bool:
        """Suppress alerts of a specific type for a duration."""
        try:
            suppressed_until = datetime.now() + timedelta(minutes=duration_minutes)
            pattern = f"{source}_{metric_name}"
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO alert_suppressions
                VALUES (?, ?, ?, ?)
            ''', (pattern, suppressed_until.isoformat(), "system", reason))
            
            conn.commit()
            conn.close()
            
            self.suppressed_alerts.add(pattern)
            logger.info(f"Suppressed alerts for {pattern} until {suppressed_until}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to suppress alert type: {str(e)}")
            return False
    
    def get_active_alerts(self, severity_filter: AlertSeverity = None) -> List[Alert]:
        """Get list of active alerts, optionally filtered by severity."""
        alerts = list(self.active_alerts.values())
        
        if severity_filter:
            alerts = [alert for alert in alerts if alert.severity == severity_filter]
        
        # Sort by severity and timestamp
        severity_order = {
            AlertSeverity.CRITICAL: 0,
            AlertSeverity.HIGH: 1,
            AlertSeverity.MEDIUM: 2,
            AlertSeverity.LOW: 3
        }
        
        alerts.sort(key=lambda x: (severity_order[x.severity], x.timestamp), reverse=True)
        return alerts
    
    def get_alert_history(self, hours: int = 24, limit: int = 100) -> List[Alert]:
        """Get alert history for specified time period."""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT * FROM alerts 
                WHERE timestamp > ?
                ORDER BY timestamp DESC
                LIMIT ?
            ''', (cutoff_time.isoformat(), limit))
            
            rows = cursor.fetchall()
            alerts = [self._row_to_alert(row) for row in rows]
            
            conn.close()
            return alerts
            
        except Exception as e:
            logger.error(f"Failed to get alert history: {str(e)}")
            return []
    
    def get_alert_statistics(self, hours: int = 24) -> Dict:
        """Get alert statistics for dashboard."""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Total alerts by severity
            cursor.execute('''
                SELECT severity, COUNT(*) 
                FROM alerts 
                WHERE timestamp > ?
                GROUP BY severity
            ''', (cutoff_time.isoformat(),))
            
            severity_counts = dict(cursor.fetchall())
            
            # Alert resolution rate
            cursor.execute('''
                SELECT 
                    COUNT(CASE WHEN status = 'RESOLVED' THEN 1 END) as resolved,
                    COUNT(*) as total
                FROM alerts 
                WHERE timestamp > ?
            ''', (cutoff_time.isoformat(),))
            
            resolved, total = cursor.fetchone()
            resolution_rate = (resolved / total * 100) if total > 0 else 0
            
            # Top alert sources
            cursor.execute('''
                SELECT source, COUNT(*) 
                FROM alerts 
                WHERE timestamp > ?
                GROUP BY source
                ORDER BY COUNT(*) DESC
                LIMIT 5
            ''', (cutoff_time.isoformat(),))
            
            top_sources = dict(cursor.fetchall())
            
            conn.close()
            
            return {
                'active_alerts': len(self.active_alerts),
                'total_alerts_24h': total,
                'resolution_rate': round(resolution_rate, 2),
                'severity_breakdown': severity_counts,
                'top_sources': top_sources,
                'critical_alerts': len([a for a in self.active_alerts.values() 
                                      if a.severity == AlertSeverity.CRITICAL])
            }
            
        except Exception as e:
            logger.error(f"Failed to get alert statistics: {str(e)}")
            return {}
    
    def create_system_alert(self, metric_name: str, current_value: float, 
                           threshold: float, severity: AlertSeverity = AlertSeverity.MEDIUM):
        """Helper method to create common system alerts."""
        
        alert_templates = {
            'cpu_usage': {
                'title': f'High CPU Usage Alert',
                'message': f'CPU usage has exceeded threshold. Current: {current_value}%, Threshold: {threshold}%'
            },
            'memory_usage': {
                'title': f'High Memory Usage Alert', 
                'message': f'Memory usage has exceeded threshold. Current: {current_value}%, Threshold: {threshold}%'
            },
            'disk_usage': {
                'title': f'Low Disk Space Alert',
                'message': f'Disk usage has exceeded threshold. Current: {current_value}%, Threshold: {threshold}%'
            },
            'process_count': {
                'title': f'High Process Count Alert',
                'message': f'Process count has exceeded threshold. Current: {current_value}, Threshold: {threshold}'
            },
            'network_error': {
                'title': f'Network Connectivity Alert',
                'message': f'Network error rate has exceeded threshold. Current: {current_value}, Threshold: {threshold}'
            }
        }
        
        template = alert_templates.get(metric_name, {
            'title': f'{metric_name.title()} Alert',
            'message': f'{metric_name} value {current_value} exceeded threshold {threshold}'
        })
        
        # Determine severity based on how much threshold is exceeded
        threshold_ratio = current_value / threshold if threshold > 0 else 1
        if threshold_ratio > 2.0:
            severity = AlertSeverity.CRITICAL
        elif threshold_ratio > 1.5:
            severity = AlertSeverity.HIGH
        elif threshold_ratio > 1.2:
            severity = AlertSeverity.MEDIUM
        else:
            severity = AlertSeverity.LOW
        
        return self.create_alert(
            title=template['title'],
            message=template['message'],
            severity=severity,
            source='system_monitor',
            metric_name=metric_name,
            metric_value=current_value,
            threshold=threshold,
            tags=['automated', 'system']
        )
    
    def update_alert_action(self, alert_id: str, action_taken: str) -> bool:
        """Update alert with action that was taken."""
        try:
            if alert_id in self.active_alerts:
                self.active_alerts[alert_id].action_taken = action_taken
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                UPDATE alerts 
                SET action_taken = ?
                WHERE id = ?
            ''', (action_taken, alert_id))
            
            conn.commit()
            conn.close()
            
            logger.info(f"Updated alert {alert_id} with action: {action_taken}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to update alert action: {str(e)}")
            return False
    
    def cleanup_old_alerts(self, days: int = 30):
        """Clean up old resolved alerts from database."""
        try:
            cutoff_time = datetime.now() - timedelta(days=days)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                DELETE FROM alerts 
                WHERE status = 'RESOLVED' AND timestamp < ?
            ''', (cutoff_time.isoformat(),))
            
            deleted_count = cursor.rowcount
            conn.commit()
            conn.close()
            
            logger.info(f"Cleaned up {deleted_count} old alerts")
            
        except Exception as e:
            logger.error(f"Failed to cleanup old alerts: {str(e)}")
    
    def export_alerts_report(self, hours: int = 24, format: str = 'json') -> str:
        """Export alerts report in specified format."""
        alerts = self.get_alert_history(hours)
        stats = self.get_alert_statistics(hours)
        
        report_data = {
            'generated_at': datetime.now().isoformat(),
            'time_period_hours': hours,
            'statistics': stats,
            'alerts': []
        }
        
        for alert in alerts:
            report_data['alerts'].append({
                'id': alert.id,
                'timestamp': alert.timestamp.isoformat(),
                'severity': alert.severity.value,
                'status': alert.status.value,
                'title': alert.title,
                'message': alert.message,
                'source': alert.source,
                'metric_name': alert.metric_name,
                'metric_value': alert.metric_value,
                'threshold': alert.threshold,
                'action_taken': alert.action_taken,
                'resolved_at': alert.resolved_at.isoformat() if alert.resolved_at else None
            })
        
        if format.lower() == 'json':
            return json.dumps(report_data, indent=2)
        else:
            # Could add CSV, HTML formats here
            return json.dumps(report_data, indent=2)

# Example usage and testing
if __name__ == "__main__":
    # Test configuration
    test_config = {
        'SMTP_SERVER': 'smtp.gmail.com',
        'SMTP_PORT': 587,
        'SMTP_USE_TLS': True,
        'FROM_EMAIL': '<EMAIL>',
        'TO_EMAILS': ['<EMAIL>'],
        'EMAIL_ALERTS': True,
        'ALERT_COOLDOWN_MINUTES': 5,
        'MAX_ALERTS_PER_HOUR': 10
    }
    
    # Initialize alert manager
    alert_manager = AlertManager(test_config)
    
    # Test creating alerts
    print("Testing alert creation...")
    
    # Create test alerts
    alert1 = alert_manager.create_system_alert('cpu_usage', 85.5, 80.0)
    alert2 = alert_manager.create_system_alert('memory_usage', 92.1, 85.0)
    alert3 = alert_manager.create_system_alert('disk_usage', 95.8, 90.0)
    
    # Display active alerts
    print(f"\nActive alerts: {len(alert_manager.get_active_alerts())}")
    for alert in alert_manager.get_active_alerts():
        print(f"  [{alert.severity.value}] {alert.title} - {alert.message}")
    
    # Test alert acknowledgment
    if alert1:
        alert_manager.acknowledge_alert(alert1.id, "admin")
        print(f"Alert {alert1.id} acknowledged")
    
    # Display statistics
    stats = alert_manager.get_alert_statistics()
    print(f"\nAlert Statistics:")
    print(f"  Active alerts: {stats.get('active_alerts', 0)}")
    print(f"  Critical alerts: {stats.get('critical_alerts', 0)}")
    print(f"  Resolution rate: {stats.get('resolution_rate', 0)}%")