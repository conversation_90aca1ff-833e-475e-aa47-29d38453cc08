#!/usr/bin/env python3
"""
Detailed test script for daemon mode functionality
"""

import time
import threading
from collector import SystemCollector
from database import DatabaseManager
from alerts import AlertManager
from detector import AnomalyDetector

def test_individual_components():
    """Test each component individually"""
    print("🔧 Testing Individual Components")
    print("=" * 50)
    
    # Test 1: System Collector
    print("1. Testing SystemCollector...")
    try:
        collector = SystemCollector()
        metrics = collector.collect_all_metrics()
        print(f"   ✅ Collected {len(metrics)} metric types")
        for key, value in metrics.items():
            if isinstance(value, dict) and 'usage_percent' in value:
                print(f"   📊 {key}: {value['usage_percent']:.1f}%")
            elif isinstance(value, (int, float)):
                print(f"   📊 {key}: {value}")
    except Exception as e:
        print(f"   ❌ SystemCollector failed: {e}")
    
    # Test 2: Database Manager
    print("\n2. Testing DatabaseManager...")
    try:
        db = DatabaseManager()
        # Insert a test metric
        db.insert_metric('test_cpu', 45.5, '%')
        print("   ✅ Database operations working")
        
        # Check recent metrics
        recent = db.get_recent_metrics(hours=1)
        print(f"   📊 Found {len(recent)} recent metrics")
    except Exception as e:
        print(f"   ❌ DatabaseManager failed: {e}")
    
    # Test 3: Alert Manager
    print("\n3. Testing AlertManager...")
    try:
        alert_config = {
            'EMAIL_ALERTS': False,
            'ALERT_COOLDOWN_MINUTES': 5,
            'MAX_ALERTS_PER_HOUR': 10
        }
        alert_manager = AlertManager(alert_config)
        print("   ✅ AlertManager initialized")
        
        # Create a test alert
        alert_manager.create_system_alert('test_metric', 85.0, 80.0, 'HIGH')
        print("   ✅ Test alert created")
    except Exception as e:
        print(f"   ❌ AlertManager failed: {e}")
    
    # Test 4: Anomaly Detector
    print("\n4. Testing AnomalyDetector...")
    try:
        detector = AnomalyDetector()
        
        # Create sample data
        current_metrics = {
            'cpu': {'usage_percent': 75.0},
            'memory': {'usage_percent': 80.0},
            'disk': {'partitions': {'C:': {'usage_percent': 45.0}}}
        }
        
        historical_data = [
            {'timestamp': '2024-01-01 12:00:00', 'cpu_usage': 50.0},
            {'timestamp': '2024-01-01 12:01:00', 'cpu_usage': 55.0},
            {'timestamp': '2024-01-01 12:02:00', 'cpu_usage': 52.0}
        ]
        
        anomalies = detector.detect_all_anomalies(current_metrics, historical_data)
        print(f"   ✅ Anomaly detection completed: {anomalies.get('total_count', 0)} anomalies")
    except Exception as e:
        print(f"   ❌ AnomalyDetector failed: {e}")

def test_daemon_simulation():
    """Simulate daemon mode manually"""
    print("\n🤖 Simulating Daemon Mode")
    print("=" * 50)
    
    try:
        # Initialize components
        collector = SystemCollector()
        db = DatabaseManager()
        
        print("📊 Starting data collection simulation...")
        
        # Collect metrics 3 times with 2-second intervals
        for i in range(3):
            print(f"   Collection cycle {i+1}/3...")
            
            # Collect metrics
            metrics = collector.collect_all_metrics()
            
            # Store in database
            for metric_type, data in metrics.items():
                if isinstance(data, dict) and 'usage_percent' in data:
                    db.insert_metric(metric_type, data['usage_percent'], '%')
                    print(f"     📈 Stored {metric_type}: {data['usage_percent']:.1f}%")
                elif isinstance(data, (int, float)):
                    db.insert_metric(metric_type, data)
                    print(f"     📈 Stored {metric_type}: {data}")
            
            if i < 2:  # Don't sleep after the last iteration
                time.sleep(2)
        
        # Check stored data
        print("\n📊 Checking stored data...")
        recent_metrics = db.get_recent_metrics(hours=1)
        print(f"✅ Successfully stored {len(recent_metrics)} metrics")
        
        if recent_metrics:
            print("📈 Recent metrics:")
            for metric in recent_metrics[-5:]:  # Show last 5
                print(f"   - {metric.get('metric_type', 'unknown')}: {metric.get('value', 'N/A')} {metric.get('unit', '')} at {metric.get('timestamp', 'unknown')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Daemon simulation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("🚀 Autonomous System Manager - Daemon Mode Test")
    print("=" * 60)
    
    # Test individual components
    test_individual_components()
    
    # Test daemon simulation
    success = test_daemon_simulation()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 All daemon mode tests completed successfully!")
        print("✅ The daemon mode functionality is working correctly")
    else:
        print("⚠️  Some tests failed - check the output above")
    
    return success

if __name__ == "__main__":
    try:
        success = main()
        exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted")
        exit(0)
