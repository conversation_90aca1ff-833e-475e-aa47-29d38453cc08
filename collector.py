import psutil
import time
import socket
import subprocess
import logging
from datetime import datetime
from typing import Dict, List, Optional
import config

class SystemCollector:
    """Collects system metrics and performance data"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.last_network_io = None
        self.last_disk_io = None
        self.boot_time = psutil.boot_time()
        
    def collect_all_metrics(self) -> Dict:
        """Collect all system metrics at once"""
        timestamp = datetime.now()
        
        metrics = {
            'timestamp': timestamp,
            'cpu': self.get_cpu_metrics(),
            'memory': self.get_memory_metrics(),
            'disk': self.get_disk_metrics(),
            'network': self.get_network_metrics(),
            'processes': self.get_process_metrics(),
            'services': self.get_service_status(),
            'system': self.get_system_info()
        }
        
        self.logger.debug(f"Collected metrics: {metrics}")
        return metrics
    
    def get_cpu_metrics(self) -> Dict:
        """Get CPU usage metrics"""
        try:
            # Get overall CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # Get per-core usage
            cpu_per_core = psutil.cpu_percent(interval=1, percpu=True)
            
            # Get CPU frequency
            cpu_freq = psutil.cpu_freq()
            
            # Get load averages (Unix only)
            load_avg = None
            try:
                load_avg = psutil.getloadavg()
            except AttributeError:
                # Windows doesn't have load average
                pass
            
            return {
                'usage_percent': round(cpu_percent, 2),
                'per_core': [round(core, 2) for core in cpu_per_core],
                'core_count': psutil.cpu_count(),
                'frequency_mhz': round(cpu_freq.current, 2) if cpu_freq else None,
                'load_average': load_avg
            }
            
        except Exception as e:
            self.logger.error(f"Error collecting CPU metrics: {e}")
            return {'usage_percent': 0, 'error': str(e)}
    
    def get_memory_metrics(self) -> Dict:
        """Get memory usage metrics"""
        try:
            # Virtual memory
            virtual_mem = psutil.virtual_memory()
            
            # Swap memory
            swap_mem = psutil.swap_memory()
            
            return {
                'total_gb': round(virtual_mem.total / (1024**3), 2),
                'used_gb': round(virtual_mem.used / (1024**3), 2),
                'available_gb': round(virtual_mem.available / (1024**3), 2),
                'usage_percent': round(virtual_mem.percent, 2),
                'swap_total_gb': round(swap_mem.total / (1024**3), 2),
                'swap_used_gb': round(swap_mem.used / (1024**3), 2),
                'swap_percent': round(swap_mem.percent, 2)
            }
            
        except Exception as e:
            self.logger.error(f"Error collecting memory metrics: {e}")
            return {'usage_percent': 0, 'error': str(e)}
    
    def get_disk_metrics(self) -> Dict:
        """Get disk usage metrics"""
        try:
            disk_usage = {}
            
            # Get usage for all mounted disks
            for partition in psutil.disk_partitions():
                try:
                    usage = psutil.disk_usage(partition.mountpoint)
                    disk_usage[partition.mountpoint] = {
                        'total_gb': round(usage.total / (1024**3), 2),
                        'used_gb': round(usage.used / (1024**3), 2),
                        'free_gb': round(usage.free / (1024**3), 2),
                        'usage_percent': round((usage.used / usage.total) * 100, 2),
                        'filesystem': partition.fstype
                    }
                except PermissionError:
                    # Skip inaccessible partitions
                    continue
            
            # Get disk I/O statistics
            disk_io = psutil.disk_io_counters()
            io_stats = None
            if disk_io:
                io_stats = {
                    'read_bytes': disk_io.read_bytes,
                    'write_bytes': disk_io.write_bytes,
                    'read_count': disk_io.read_count,
                    'write_count': disk_io.write_count
                }
            
            return {
                'partitions': disk_usage,
                'io_stats': io_stats
            }
            
        except Exception as e:
            self.logger.error(f"Error collecting disk metrics: {e}")
            return {'partitions': {}, 'error': str(e)}
    
    def get_network_metrics(self) -> Dict:
        """Get network usage and connectivity metrics"""
        try:
            # Network I/O statistics
            net_io = psutil.net_io_counters()
            
            # Network interfaces
            interfaces = {}
            for interface, addrs in psutil.net_if_addrs().items():
                interfaces[interface] = []
                for addr in addrs:
                    interfaces[interface].append({
                        'family': str(addr.family),
                        'address': addr.address,
                        'netmask': addr.netmask
                    })
            
            # Internet connectivity test
            connectivity = self.test_connectivity()
            
            return {
                'bytes_sent': net_io.bytes_sent,
                'bytes_received': net_io.bytes_recv,
                'packets_sent': net_io.packets_sent,
                'packets_received': net_io.packets_recv,
                'interfaces': interfaces,
                'connectivity': connectivity
            }
            
        except Exception as e:
            self.logger.error(f"Error collecting network metrics: {e}")
            return {'connectivity': False, 'error': str(e)}
    
    def get_process_metrics(self) -> Dict:
        """Get process information"""
        try:
            processes = []
            total_processes = 0
            
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent', 'status']):
                try:
                    proc_info = proc.info
                    total_processes += 1
                    
                    # Only keep top processes by CPU/memory usage
                    if proc_info['cpu_percent'] > 1.0 or proc_info['memory_percent'] > 1.0:
                        processes.append({
                            'pid': proc_info['pid'],
                            'name': proc_info['name'],
                            'cpu_percent': round(proc_info['cpu_percent'], 2),
                            'memory_percent': round(proc_info['memory_percent'], 2),
                            'status': proc_info['status']
                        })
                        
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            # Sort by CPU usage
            processes.sort(key=lambda x: x['cpu_percent'], reverse=True)
            
            return {
                'total_count': total_processes,
                'top_processes': processes[:20],  # Top 20 processes
                'high_cpu_count': len([p for p in processes if p['cpu_percent'] > 10]),
                'high_memory_count': len([p for p in processes if p['memory_percent'] > 5])
            }
            
        except Exception as e:
            self.logger.error(f"Error collecting process metrics: {e}")
            return {'total_count': 0, 'error': str(e)}
    
    def get_service_status(self) -> Dict:
        """Check status of critical services"""
        services = {}
        
        for service in config.CRITICAL_SERVICES:
            try:
                # Try systemctl first (Linux)
                result = subprocess.run(
                    ['systemctl', 'is-active', service],
                    capture_output=True,
                    text=True,
                    timeout=5
                )
                
                services[service] = {
                    'status': 'running' if result.returncode == 0 else 'stopped',
                    'method': 'systemctl'
                }
                
            except (subprocess.TimeoutExpired, FileNotFoundError):
                # Fallback: check if process is running
                try:
                    for proc in psutil.process_iter(['name']):
                        if service.lower() in proc.info['name'].lower():
                            services[service] = {
                                'status': 'running',
                                'method': 'process_check'
                            }
                            break
                    else:
                        services[service] = {
                            'status': 'unknown',
                            'method': 'process_check'
                        }
                except Exception:
                    services[service] = {
                        'status': 'unknown',
                        'method': 'failed'
                    }
        
        return services
    
    def get_system_info(self) -> Dict:
        """Get general system information"""
        try:
            return {
                'hostname': socket.gethostname(),
                'platform': psutil.LINUX if hasattr(psutil, 'LINUX') else 'unknown',
                'boot_time': datetime.fromtimestamp(self.boot_time),
                'uptime_hours': round((time.time() - self.boot_time) / 3600, 2),
                'users_count': len(psutil.users()),
                'python_version': f"{psutil.version_info[0]}.{psutil.version_info[1]}"
            }
            
        except Exception as e:
            self.logger.error(f"Error collecting system info: {e}")
            return {'error': str(e)}
    
    def test_connectivity(self) -> Dict:
        """Test internet connectivity"""
        connectivity = {
            'internet': False,
            'dns': False,
            'ping_results': {}
        }
        
        try:
            # Test DNS resolution
            socket.gethostbyname('google.com')
            connectivity['dns'] = True
            
            # Test ping to various hosts
            for host in config.PING_HOSTS:
                try:
                    start_time = time.time()
                    socket.create_connection((host, 80), timeout=config.NETWORK_TIMEOUT)
                    end_time = time.time()
                    
                    connectivity['ping_results'][host] = {
                        'success': True,
                        'response_time_ms': round((end_time - start_time) * 1000, 2)
                    }
                    connectivity['internet'] = True
                    
                except Exception as e:
                    connectivity['ping_results'][host] = {
                        'success': False,
                        'error': str(e)
                    }
                    
        except Exception as e:
            self.logger.error(f"Error testing connectivity: {e}")
            connectivity['error'] = str(e)
        
        return connectivity
    
    def get_metric_value(self, metrics: Dict, metric_path: str) -> Optional[float]:
        """Extract a specific metric value by path (e.g., 'cpu.usage_percent')"""
        try:
            parts = metric_path.split('.')
            value = metrics
            
            for part in parts:
                value = value[part]
                
            return float(value) if isinstance(value, (int, float)) else None
            
        except (KeyError, TypeError, ValueError):
            return None
    
    def is_healthy(self, metrics: Dict) -> bool:
        """Quick health check based on metrics"""
        try:
            cpu_usage = self.get_metric_value(metrics, 'cpu.usage_percent')
            memory_usage = self.get_metric_value(metrics, 'memory.usage_percent')
            
            # Check main disk partition
            disk_usage = 0
            if metrics.get('disk', {}).get('partitions'):
                for partition, data in metrics['disk']['partitions'].items():
                    if partition in ['/', 'C:\\']:  # Main partitions
                        disk_usage = data.get('usage_percent', 0)
                        break
            
            # System is unhealthy if any critical threshold is exceeded
            if cpu_usage and cpu_usage > config.CPU_THRESHOLD:
                return False
            if memory_usage and memory_usage > config.MEMORY_THRESHOLD:
                return False
            if disk_usage > config.DISK_THRESHOLD:
                return False
                
            # Check critical services
            services = metrics.get('services', {})
            for service, status in services.items():
                if status.get('status') == 'stopped':
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error in health check: {e}")
            return False

# Convenience function for quick metric collection
def collect_quick_metrics() -> Dict:
    """Quick collection of essential metrics"""
    collector = SystemCollector()
    return {
        'timestamp': datetime.now(),
        'cpu_percent': psutil.cpu_percent(interval=1),
        'memory_percent': psutil.virtual_memory().percent,
        'disk_percent': psutil.disk_usage('/').percent if psutil.LINUX else psutil.disk_usage('C:\\').percent
    }