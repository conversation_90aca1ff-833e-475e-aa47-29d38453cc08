import sqlite3
import json
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path

class DatabaseManager:
    """Manages SQLite database operations for the autonomous system manager."""
    
    def __init__(self, db_path: str = "system_monitor.db"):
        self.db_path = Path(db_path)
        self.logger = logging.getLogger(__name__)
        self._init_database()
    
    def _init_database(self):
        """Initialize database with required tables."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Create metrics table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS metrics (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                        metric_type TEXT NOT NULL,
                        value REAL NOT NULL,
                        unit TEXT,
                        hostname TEXT,
                        additional_data TEXT
                    )
                ''')
                
                # Create alerts table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS alerts (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                        alert_type TEXT NOT NULL,
                        severity TEXT NOT NULL,
                        message TEXT NOT NULL,
                        metric_type TEXT,
                        threshold_value REAL,
                        actual_value REAL,
                        resolved BOOLEAN DEFAULT 0,
                        resolved_timestamp DATETIME
                    )
                ''')
                
                # Create actions table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS actions (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                        action_type TEXT NOT NULL,
                        description TEXT NOT NULL,
                        status TEXT NOT NULL,
                        error_message TEXT,
                        trigger_alert_id INTEGER,
                        execution_time REAL,
                        rollback_data TEXT,
                        FOREIGN KEY (trigger_alert_id) REFERENCES alerts (id)
                    )
                ''')
                
                # Create system_baseline table for normal operating ranges
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS system_baseline (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        metric_type TEXT UNIQUE NOT NULL,
                        mean_value REAL NOT NULL,
                        std_deviation REAL NOT NULL,
                        min_value REAL NOT NULL,
                        max_value REAL NOT NULL,
                        sample_count INTEGER NOT NULL,
                        last_updated DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # Create configuration table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS configuration (
                        key TEXT PRIMARY KEY,
                        value TEXT NOT NULL,
                        description TEXT,
                        last_modified DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # Create indexes for better performance
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_metrics_timestamp ON metrics(timestamp)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_metrics_type ON metrics(metric_type)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_alerts_timestamp ON alerts(timestamp)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_actions_timestamp ON actions(timestamp)')
                
                conn.commit()
                self.logger.info("Database initialized successfully")
                
        except sqlite3.Error as e:
            self.logger.error(f"Database initialization error: {e}")
            raise
    
    def insert_metric(self, metric_type: str, value: float, unit: str = None, 
                     hostname: str = None, additional_data: Dict = None) -> int:
        """Insert a system metric into the database."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                additional_json = json.dumps(additional_data) if additional_data else None
                
                cursor.execute('''
                    INSERT INTO metrics (metric_type, value, unit, hostname, additional_data)
                    VALUES (?, ?, ?, ?, ?)
                ''', (metric_type, value, unit, hostname, additional_json))
                
                metric_id = cursor.lastrowid
                conn.commit()
                return metric_id
                
        except sqlite3.Error as e:
            self.logger.error(f"Error inserting metric: {e}")
            raise
    
    def insert_alert(self, alert_type: str, severity: str, message: str,
                    metric_type: str = None, threshold_value: float = None,
                    actual_value: float = None) -> int:
        """Insert an alert into the database."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT INTO alerts 
                    (alert_type, severity, message, metric_type, threshold_value, actual_value)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (alert_type, severity, message, metric_type, threshold_value, actual_value))
                
                alert_id = cursor.lastrowid
                conn.commit()
                return alert_id
                
        except sqlite3.Error as e:
            self.logger.error(f"Error inserting alert: {e}")
            raise
    
    def insert_action(self, action_type: str, description: str, status: str,
                     error_message: str = None, trigger_alert_id: int = None,
                     execution_time: float = None, rollback_data: Dict = None) -> int:
        """Insert an automated action record."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                rollback_json = json.dumps(rollback_data) if rollback_data else None
                
                cursor.execute('''
                    INSERT INTO actions 
                    (action_type, description, status, error_message, trigger_alert_id, 
                     execution_time, rollback_data)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (action_type, description, status, error_message, 
                      trigger_alert_id, execution_time, rollback_json))
                
                action_id = cursor.lastrowid
                conn.commit()
                return action_id
                
        except sqlite3.Error as e:
            self.logger.error(f"Error inserting action: {e}")
            raise
    
    def get_recent_metrics(self, metric_type: str = None, hours: int = 24, 
                          limit: int = 1000) -> List[Dict]:
        """Get recent metrics from the database."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                since_time = datetime.now() - timedelta(hours=hours)
                
                if metric_type:
                    cursor.execute('''
                        SELECT * FROM metrics 
                        WHERE metric_type = ? AND timestamp >= ?
                        ORDER BY timestamp DESC
                        LIMIT ?
                    ''', (metric_type, since_time, limit))
                else:
                    cursor.execute('''
                        SELECT * FROM metrics 
                        WHERE timestamp >= ?
                        ORDER BY timestamp DESC
                        LIMIT ?
                    ''', (since_time, limit))
                
                return [dict(row) for row in cursor.fetchall()]
                
        except sqlite3.Error as e:
            self.logger.error(f"Error fetching metrics: {e}")
            return []
    
    def get_recent_alerts(self, hours: int = 24, limit: int = 100, 
                         resolved: bool = None) -> List[Dict]:
        """Get recent alerts from the database."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                since_time = datetime.now() - timedelta(hours=hours)
                
                if resolved is not None:
                    cursor.execute('''
                        SELECT * FROM alerts 
                        WHERE timestamp >= ? AND resolved = ?
                        ORDER BY timestamp DESC
                        LIMIT ?
                    ''', (since_time, resolved, limit))
                else:
                    cursor.execute('''
                        SELECT * FROM alerts 
                        WHERE timestamp >= ?
                        ORDER BY timestamp DESC
                        LIMIT ?
                    ''', (since_time, limit))
                
                return [dict(row) for row in cursor.fetchall()]
                
        except sqlite3.Error as e:
            self.logger.error(f"Error fetching alerts: {e}")
            return []
    
    def get_recent_actions(self, hours: int = 24, limit: int = 100) -> List[Dict]:
        """Get recent actions from the database."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                since_time = datetime.now() - timedelta(hours=hours)
                
                cursor.execute('''
                    SELECT a.*, al.message as trigger_message 
                    FROM actions a
                    LEFT JOIN alerts al ON a.trigger_alert_id = al.id
                    WHERE a.timestamp >= ?
                    ORDER BY a.timestamp DESC
                    LIMIT ?
                ''', (since_time, limit))
                
                return [dict(row) for row in cursor.fetchall()]
                
        except sqlite3.Error as e:
            self.logger.error(f"Error fetching actions: {e}")
            return []
    
    def update_baseline(self, metric_type: str, mean_value: float, 
                       std_deviation: float, min_value: float, 
                       max_value: float, sample_count: int):
        """Update or insert baseline statistics for a metric type."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT OR REPLACE INTO system_baseline 
                    (metric_type, mean_value, std_deviation, min_value, max_value, sample_count)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (metric_type, mean_value, std_deviation, min_value, max_value, sample_count))
                
                conn.commit()
                
        except sqlite3.Error as e:
            self.logger.error(f"Error updating baseline: {e}")
            raise
    
    def get_baseline(self, metric_type: str) -> Optional[Dict]:
        """Get baseline statistics for a metric type."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT * FROM system_baseline WHERE metric_type = ?
                ''', (metric_type,))
                
                row = cursor.fetchone()
                return dict(row) if row else None
                
        except sqlite3.Error as e:
            self.logger.error(f"Error fetching baseline: {e}")
            return None
    
    def resolve_alert(self, alert_id: int) -> bool:
        """Mark an alert as resolved."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    UPDATE alerts 
                    SET resolved = 1, resolved_timestamp = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (alert_id,))
                
                conn.commit()
                return cursor.rowcount > 0
                
        except sqlite3.Error as e:
            self.logger.error(f"Error resolving alert: {e}")
            return False
    
    def get_system_summary(self) -> Dict:
        """Get a summary of system metrics, alerts, and actions."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Count metrics in last 24 hours
                cursor.execute('''
                    SELECT COUNT(*) FROM metrics 
                    WHERE timestamp >= datetime('now', '-24 hours')
                ''')
                metrics_count = cursor.fetchone()[0]
                
                # Count unresolved alerts
                cursor.execute('SELECT COUNT(*) FROM alerts WHERE resolved = 0')
                active_alerts = cursor.fetchone()[0]
                
                # Count actions in last 24 hours
                cursor.execute('''
                    SELECT COUNT(*) FROM actions 
                    WHERE timestamp >= datetime('now', '-24 hours')
                ''')
                actions_count = cursor.fetchone()[0]
                
                # Get latest metrics for each type
                cursor.execute('''
                    SELECT metric_type, value, timestamp 
                    FROM metrics m1
                    WHERE timestamp = (
                        SELECT MAX(timestamp) 
                        FROM metrics m2 
                        WHERE m2.metric_type = m1.metric_type
                    )
                ''')
                latest_metrics = {row[0]: {'value': row[1], 'timestamp': row[2]} 
                                for row in cursor.fetchall()}
                
                return {
                    'metrics_24h': metrics_count,
                    'active_alerts': active_alerts,
                    'actions_24h': actions_count,
                    'latest_metrics': latest_metrics,
                    'database_size': self.db_path.stat().st_size if self.db_path.exists() else 0
                }
                
        except sqlite3.Error as e:
            self.logger.error(f"Error getting system summary: {e}")
            return {}
    
    def cleanup_old_data(self, days_to_keep: int = 30):
        """Remove old data beyond the specified number of days."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cutoff_date = datetime.now() - timedelta(days=days_to_keep)
                
                # Clean old metrics
                cursor.execute('DELETE FROM metrics WHERE timestamp < ?', (cutoff_date,))
                metrics_deleted = cursor.rowcount
                
                # Clean old resolved alerts
                cursor.execute('''
                    DELETE FROM alerts 
                    WHERE timestamp < ? AND resolved = 1
                ''', (cutoff_date,))
                alerts_deleted = cursor.rowcount
                
                # Clean old actions
                cursor.execute('DELETE FROM actions WHERE timestamp < ?', (cutoff_date,))
                actions_deleted = cursor.rowcount
                
                conn.commit()
                
                self.logger.info(f"Cleaned up old data: {metrics_deleted} metrics, "
                               f"{alerts_deleted} alerts, {actions_deleted} actions")
                
                return {
                    'metrics_deleted': metrics_deleted,
                    'alerts_deleted': alerts_deleted,
                    'actions_deleted': actions_deleted
                }
                
        except sqlite3.Error as e:
            self.logger.error(f"Error cleaning up old data: {e}")
            return {}
    
    def export_data(self, table_name: str, output_file: str, days: int = 7) -> bool:
        """Export table data to CSV file."""
        try:
            import csv
            
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                since_date = datetime.now() - timedelta(days=days)
                
                cursor.execute(f'''
                    SELECT * FROM {table_name} 
                    WHERE timestamp >= ?
                    ORDER BY timestamp DESC
                ''', (since_date,))
                
                rows = cursor.fetchall()
                
                if rows:
                    with open(output_file, 'w', newline='') as csvfile:
                        writer = csv.DictWriter(csvfile, fieldnames=rows[0].keys())
                        writer.writeheader()
                        for row in rows:
                            writer.writerow(dict(row))
                    
                    self.logger.info(f"Exported {len(rows)} rows to {output_file}")
                    return True
                else:
                    self.logger.warning(f"No data found in {table_name} for export")
                    return False
                    
        except Exception as e:
            self.logger.error(f"Error exporting data: {e}")
            return False
    
    def get_metric_statistics(self, metric_type: str, hours: int = 24) -> Dict:
        """Get statistical analysis of a specific metric."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                since_time = datetime.now() - timedelta(hours=hours)
                
                cursor.execute('''
                    SELECT 
                        COUNT(*) as count,
                        AVG(value) as mean,
                        MIN(value) as min_val,
                        MAX(value) as max_val
                    FROM metrics 
                    WHERE metric_type = ? AND timestamp >= ?
                ''', (metric_type, since_time))
                
                result = cursor.fetchone()
                
                if result and result[0] > 0:
                    return {
                        'count': result[0],
                        'mean': round(result[1], 2),
                        'min': result[2],
                        'max': result[3],
                        'range': result[3] - result[2]
                    }
                else:
                    return {}
                    
        except sqlite3.Error as e:
            self.logger.error(f"Error getting metric statistics: {e}")
            return {}
    
    def close(self):
        """Close database connection (placeholder for cleanup if needed)."""
        # SQLite connections are closed automatically with context managers
        self.logger.info("Database manager closed")